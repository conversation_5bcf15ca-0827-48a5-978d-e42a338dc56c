const deepFreeze = require('deep-freeze');
const { getSign } = require('horoscope');
const momentTz = require('moment-timezone');
const moment = require('moment');

const horoscopes = [
  'Capricorn',
  'Aquarius',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Sagittarius'
];
deepFreeze(horoscopes);

function getHoroscope(date) {
  return getSign(
    {
      month: date.getMonth() + 1, // In JS Date, 0 is Jan
      day: date.getDate(),
    },
    true,
  ); // if error, return null instead of throwing
}

function getAge(date, timezone) {
  if (!date) {
    return null;
  }
  let age;
  if (timezone) {
    // using this approach because we will not change the birthday to the user's timezone because we don't take hr and min into account so day must be always same
    const now = momentTz.tz(timezone);
    age = now.year() - date.getFullYear();
    if (now.month() < date.getMonth() || (now.month() === date.getMonth() && now.date() < date.getDate())) {
      age = age - 1;
    }
  } else {
    age = moment().diff(date, 'years');
  }

  return age;
}

module.exports = {
  horoscopes,
  getHoroscope,
  getAge,
};
