const { compareFaces } = require('../lib/rekognition');
const s3 = require('../lib/s3');

async function verifyProfilePicture(user) {
  let profilePicture = user.pictures[0];
  let referenceImage = user.verification.faceComparisonReferenceImage;

  if (!profilePicture) {
    return 'reject';
  }

  if (!referenceImage) {
    if (user.verification.pictures.length > 0) {
      referenceImage = user.verification.pictures.at(-1);
    } else if (user.livenessVerification) {
      referenceImage = user.livenessVerification.frames[0].key;
    } else {
      return 'reject';
    }
  }

  // for videos, use the first thumbnail
  if (!['.jpg', '.jpeg', '.png'].some((ext) => profilePicture.toLowerCase().includes(ext))) {
    const keys = await s3.listDirectory(profilePicture);
    const firstThumbnail = keys.find(x => x.includes('thumb.0000000.jpg'));
    if (firstThumbnail) {
      profilePicture = firstThumbnail;
    }
  }

  const found = user.verification.faceComparisonResults.find(x => x.image == profilePicture);
  if (found) {
    return found.result;
  }

  let result;
  if (!['.jpg', '.jpeg', '.png'].some((ext) => profilePicture.toLowerCase().includes(ext))) {
    result = 'reject';
  } else {
    const data = await compareFaces(referenceImage, profilePicture);
    if (data.FaceMatches?.length > 0 && !(data.UnmatchedFaces?.length > 0)) {
      result = 'verify';
    }
    else {
      result = 'reject';
    }
  }

  user.verification.faceComparisonResults.push({
    image: profilePicture,
    result: result,
  });

  return result;
}

function setFaceComparisonReferenceImage(user, referenceImage) {
  user.verification.faceComparisonReferenceImage = referenceImage;
  user.verification.faceComparisonResults = [];
}

module.exports = {
  verifyProfilePicture,
  setFaceComparisonReferenceImage,
}

