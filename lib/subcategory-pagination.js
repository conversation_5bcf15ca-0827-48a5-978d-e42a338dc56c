const Profile = require('../models/profile');
const Subcategory = require('../models/subcategory');
const SubcategoryPaginatedProfiles = require('../models/subcategory-paginated-profiles');
const { getSitemapPaginationPageSize } = require('./constants');


async function getAllSubcategoryIds() {
  const subcategories = await Subcategory.find({}, { id: 1 }).lean();
  return subcategories.map(sub => sub.id);
}

async function getPaginatedProfileIds(subcategoryId, pageNo) {
  const paginatedData = await SubcategoryPaginatedProfiles
    .findOne({ subcategoryId, pageNo })
    .select({ profileIds: 1, _id: 0 })
    .lean();
  
  return paginatedData ? paginatedData.profileIds : null;
}

async function createPaginatedProfilesForSubcategory(subcategoryId) {
  const pageSize = getSitemapPaginationPageSize();
  
  const profiles = await Profile
    .find({ subcategories: subcategoryId })
    .select({ id: 1, _id: 0 })
    .sort({ sort: 1, id: 1 })
    .lean();

  const totalProfiles = profiles.length;
  const totalPages = Math.ceil(totalProfiles / pageSize);

  await SubcategoryPaginatedProfiles.deleteMany({ subcategoryId });


  const bulkOps = [];
  for (let pageNo = 1; pageNo <= totalPages; pageNo++) {
    const startIndex = (pageNo - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, totalProfiles);
    const profileIds = profiles.slice(startIndex, endIndex).map(p => p.id);

    bulkOps.push({
      insertOne: {
        document: {
          subcategoryId,
          pageNo,
          profileIds,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      }
    });
  }

  if (bulkOps.length > 0) {
    await SubcategoryPaginatedProfiles.bulkWrite(bulkOps);
  }

  return totalPages;
}

async function updateSubcategoryTotalPages(subcategoryId, totalPages) {
  await Subcategory.updateOne(
    { id: subcategoryId },
    { $set: { totalProfilePages: totalPages } }
  );
}

async function cleanupExcessPages(subcategoryId, currentTotalPages) {
  await SubcategoryPaginatedProfiles.deleteMany({
    subcategoryId,
    pageNo: { $gt: currentTotalPages }
  });
}

async function getTotalPagesForSubcategory(subcategoryId) {
  const maxPageDoc = await SubcategoryPaginatedProfiles
    .findOne({ subcategoryId })
    .sort({ pageNo: -1 })
    .select({ pageNo: 1, _id: 0 })
    .lean();
  
  return maxPageDoc ? maxPageDoc.pageNo : 0;
}

async function backfillSubcategoryPagination(options = {}) {
  const { batchSize = 5, logProgress = true } = options;

  if (logProgress) {
    console.log('Starting subcategory pagination backfill...');
  }

  const subcategoryIds = await getAllSubcategoryIds();
  const totalSubcategories = subcategoryIds.length;

  if (logProgress) {
    console.log(`Found ${totalSubcategories} subcategories to process`);
  }

  let processedSubcategories = 0;
  let totalPages = 0;
  let totalProfiles = 0;
  const errors = [];

  // Process subcategories in batches to avoid overwhelming the database
  for (let i = 0; i < subcategoryIds.length; i += batchSize) {
    const batch = subcategoryIds.slice(i, i + batchSize);

    const batchPromises = batch.map(async (subcategoryId) => {
      try {
        const result = await processSubcategory(subcategoryId);
        return result;
      } catch (error) {
        console.error(`Error processing subcategory ${subcategoryId}:`, error);
        errors.push({
          subcategoryId,
          error: error.message
        });
        return null;
      }
    });

    const batchResults = await Promise.all(batchPromises);

    // Aggregate results
    batchResults.forEach(result => {
      if (result) {
        processedSubcategories++;
        totalPages += result.totalPages;
        totalProfiles += result.profileCount;
      }
    });

    if (logProgress) {
      console.log(`Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(subcategoryIds.length / batchSize)}: ${processedSubcategories}/${totalSubcategories} subcategories completed`);
    }
  }

  const summary = {
    totalSubcategories,
    processedSubcategories,
    totalPages,
    totalProfiles,
    errors
  };

  if (logProgress) {
    console.log('Backfill completed!');
    console.log(`Summary:`, summary);
  }

  return summary;
}

module.exports = {
  getAllSubcategoryIds,
  getPaginatedProfileIds,
  createPaginatedProfilesForSubcategory,
  updateSubcategoryTotalPages,
  cleanupExcessPages,
  getTotalPagesForSubcategory,
  processSubcategory,
  backfillSubcategoryPagination
};
