const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const subcategoryPaginatedProfilesSchema = new mongoose.Schema({
  subcategoryId: { type: Number, required: true },
  pageNo: { type: Number, required: true },
  profileIds: [{ type: Number }], // Array of profile IDs, max 100 per page
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Compound index for efficient querying by subcategoryId and pageNo
subcategoryPaginatedProfilesSchema.index({
  subcategoryId: 1,
  pageNo: 1,
}, { unique: true });

// Index for cleanup operations
subcategoryPaginatedProfilesSchema.index({
  subcategoryId: 1,
});

// Update the updatedAt field on save
subcategoryPaginatedProfilesSchema.pre('save', function (next) {
  this.updatedAt = new Date();
  next();
});

// Export schema
let conn = connectionLib.getPersonalityDatabaseConnection() || mongoose;
module.exports = conn.model('SubcategoryPaginatedProfiles', subcategoryPaginatedProfilesSchema);
