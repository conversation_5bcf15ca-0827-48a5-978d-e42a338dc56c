const {
  app, mongoose, validImagePath, validVideoPath,
} = require('./common');
const { expect } = require('chai');
const { assert } = require('chai');
const { notifs, reset, waitFor } = require('./stub');
const request = require('supertest');
const temp = require('temp').track();
const fs = require('fs');
const sinon = require('sinon');
const stub = require('./stub');
const User = require('../models/user');
const LivenessChallenge = require('../models/liveness-challenge');
const constants = require('../lib/constants');

describe('liveness', () => {
  const challengeId = '1110f6e4-a225-11ed-bf55-d33b0802ecdc';

  beforeEach(async () => {
    constants.hideUnverifiedUsers.restore();
    sinon.stub(constants, 'hideUnverifiedUsers').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('soft ban for unverified users');
  });

  it('basic functionality', async () => {
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    expect(res.body.pictures.length).to.equal(1);

    // start
    fakeLambda.invoke = function (params) {
      expect(params.FunctionName).to.equal('liveness-detection');
      const payload = JSON.parse(params.Payload);
      expect(payload.route).to.equal('start');
      expect(payload.imageWidth).to.equal(1000);
      expect(payload.imageHeight).to.equal(1000);
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            id: challengeId,
            imageWidth: 1000,
            imageHeight: 1000,
            areaLeft: 218,
            areaTop: 125,
            areaWidth: 562,
            areaHeight: 750,
            minFaceAreaPercent: 50,
            noseLeft: 412,
            noseTop: 525,
            noseWidth: 20,
            noseHeight: 20,
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/start')
      .set('authorization', 0)
      .send({ imageWidth: 1000, imageHeight: 1000 });
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({
      challenge: {
        id: challengeId,
        imageWidth: 1000,
        imageHeight: 1000,
        areaLeft: 218,
        areaTop: 125,
        areaWidth: 562,
        areaHeight: 750,
        minFaceAreaPercent: 50,
        noseLeft: 412,
        noseTop: 525,
        noseWidth: 20,
        noseHeight: 20,
      }
    });

    // post frames
    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    livenessChallenges = await LivenessChallenge.find();
    expect(livenessChallenges.length).to.equal(1);
    expect(livenessChallenges[0].frames.length).to.equal(2);
    expect(livenessChallenges[0].frames[0].key).to.contain(`0/liveness/${challengeId}/`);

    // verify
    fakeLambda.invoke = function (params) {
      expect(params.FunctionName).to.equal('liveness-detection');
      const payload = JSON.parse(params.Payload);
      expect(payload.route).to.equal('verify');
      expect(payload.bucket_name).to.equal('MOCK_S3_BUCKET');
      expect(payload.challenge).to.include({
        id: challengeId,
        imageWidth: 1000,
        imageHeight: 1000,
        areaLeft: 218,
        areaTop: 125,
        areaWidth: 562,
        areaHeight: 750,
        minFaceAreaPercent: 50,
        noseLeft: 412,
        noseTop: 525,
        noseWidth: 20,
        noseHeight: 20,
      });
      expect(payload.challenge.frames.length).to.equal(2);
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: true
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.verifyProfile.received).to.equal(false);
    const initialCoins = res.body.coins;

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    livenessChallenges = await LivenessChallenge.find();
    expect(livenessChallenges.length).to.equal(1);
    expect(livenessChallenges[0].livenessSuccess).to.equal(true);
    expect(livenessChallenges[0].livenessFailureReason).to.equal();

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.verifyProfile.received).to.equal(true);
    expect(res.body.coins).to.equal(initialCoins + 200);

    // user should be unbanned after verifying
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();
  });

});

describe('verify', () => {
  const challengeId = '1110f6e4-a225-11ed-bf55-d33b0802ecdc';

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.12.1' });
    expect(res.status).to.equal(200);

    // start
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            id: challengeId,
            imageWidth: 1000,
            imageHeight: 1000,
            areaLeft: 218,
            areaTop: 125,
            areaWidth: 562,
            areaHeight: 750,
            minFaceAreaPercent: 50,
            noseLeft: 412,
            noseTop: 525,
            noseWidth: 20,
            noseHeight: 20,
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/start')
      .set('authorization', 0)
      .send({ imageWidth: 1000, imageHeight: 1000 });
    expect(res.status).to.equal(200);

    // post frames
    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);
  });

  it('challenge failed', async () => {

    // update version to receive rejection notification
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.30' });
    expect(res.status).to.equal(200);

    // verify
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: false,
            failure_reason: 'Nose not inside nose box',
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Nose challenge failed.');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Nose challenge failed.');

    livenessChallenges = await LivenessChallenge.find();
    expect(livenessChallenges.length).to.equal(1);
    expect(livenessChallenges[0].livenessSuccess).to.equal(false);
    expect(livenessChallenges[0].livenessFailureReason).to.equal('Nose not inside nose box');
    expect(livenessChallenges[0].rejectionReason).to.equal('Nose challenge failed.');

    // cannot call the route again
    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(403);

    // request manual verification
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { support: true };
    res = await user.save();

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .post('/v1/liveness/requestManualVerification')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');
    expect(res.body.users[0].livenessVerification.id).to.equal(challengeId);
    expect(res.body.users[0].livenessVerification.rejectionReason).to.equal('Nose challenge failed.');

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '0',
        verified: false,
        rejectionReason: 'Nose challenge failed.',
      });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Verification Unsuccessful');
    expect(notifs.recent.notification.body).to.equal('Reason: Nose challenge failed. Please follow the profile verification guidelines.');
    expect(notifs.recent.data).to.eql({
      verification: JSON.stringify({
        verificationStatus: 'rejected',
        verified: false,
        rejectionReason: 'Nose challenge failed.',
      }),
    });
    expect(notifs.numSent).to.equal(1);
    reset();
  });

  it('ignore if user already verified', async () => {
    // verify
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: false,
            failure_reason: 'Nose not inside nose box',
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    user = await User.findById('0');
    user.verification.status = 'verified';
    await user.save();

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');
    expect(res.body.rejectionReason).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
    expect(res.body.user.rejectionReason).to.equal();
  });

  // test is disabled because the logic for auto-rejecting manual verification
  // for banned users is disabled (see setVerificationStatus)
  /*
  it('shadow banned user', async () => {
    user = await User.findOne({ _id: 0 });
    user.shadowBanned = true;
    await user.save();

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Nose challenge failed.');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Nose challenge failed.');

    livenessChallenges = await LivenessChallenge.find();
    expect(livenessChallenges.length).to.equal(1);
    expect(livenessChallenges[0].livenessSuccess).to.equal(null);
    expect(livenessChallenges[0].livenessFailureReason).to.equal(null);
    expect(livenessChallenges[0].rejectionReason).to.equal('User is banned.');

    // request manual verification
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { support: true };
    res = await user.save();

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .post('/v1/liveness/requestManualVerification')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('pending');

    // unban user
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
  });
  */

  it('some face matches', async () => {
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // verify
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: true
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    let i = 0;
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        if (i++ % 2) {
          resolve({ FaceMatches: [ {} ] });
        } else {
          resolve({ UnmatchedFaces: [ {} ] });
        }
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('pending');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('pending');

    // manually verify
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { support: true };
    res = await user.save();

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');
    expect(res.body.users[0].livenessVerification.id).to.equal(challengeId);

    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '0',
        verified: true,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);

    livenessChallenges = await LivenessChallenge.find();
    expect(livenessChallenges.length).to.equal(1);
    expect(livenessChallenges[0].rejectionReason).to.equal('Some profile pictures contain a different face.');
    expect(livenessChallenges[0].manuallyCheckedBy).to.equal('0');
    expect(livenessChallenges[0].manuallyCheckedResult).to.equal(true);

    user = await User.findById('0');
    expect(user.livenessVerification.rejectionReason).to.equal('Some profile pictures contain a different face.');
    expect(user.livenessVerification.manuallyCheckedBy).to.equal('0');
    expect(user.livenessVerification.manuallyCheckedResult).to.equal(true);
  });

  it('no face matches', async () => {
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // verify
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: true
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ UnmatchedFaces: [ {} ] });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Verification does not match profile pictures.');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Verification does not match profile pictures.');

    res = await request(app)
      .get('/v1/user/verificationStatus')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Verification does not match profile pictures.');
  });

  it('no facial photos', async () => {
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // verify
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: true
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({});
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Verification does not match profile pictures.');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Verification does not match profile pictures.');
  });

  it('timeout', async () => {
    livenessChallenges = await LivenessChallenge.find();
    livenessChallenges[0].date = 0;
    await livenessChallenges[0].save();

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(403);
  });
});

describe('old reverification', () => {
  const challengeId = '1110f6e4-a225-11ed-bf55-d33b0802ecdc';

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.12.1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { support: true };
    res = await user.save();

    // start
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            id: challengeId,
            imageWidth: 1000,
            imageHeight: 1000,
            areaLeft: 218,
            areaTop: 125,
            areaWidth: 562,
            areaHeight: 750,
            minFaceAreaPercent: 50,
            noseLeft: 412,
            noseTop: 525,
            noseWidth: 20,
            noseHeight: 20,
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/start')
      .set('authorization', 0)
      .send({ imageWidth: 1000, imageHeight: 1000 });
    expect(res.status).to.equal(200);

    // post frames
    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    // verify
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: true
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');
  });

  it('add picture that matches', async () => {
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/user/verificationStatus')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');
    expect(res.body.rejectionReason).to.equal();
  });

  it('add picture that does not matches', async () => {
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ UnmatchedFaces: [ {} ] });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('reverifying');

    res = await request(app)
      .get('/v1/user/verificationStatus')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('reverifying');
    expect(res.body.rejectionReason).to.equal();
  });

  it('delete face picture none remaining', async () => {
    // add picture without face
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');

    // delete first face picture
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(2);
    const pictures = res.body.user.pictures;

    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 0)
      .query({ id: pictures[0] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('reverifying');
  });

  it('delete face picture some remaining', async () => {
    // add picture with face
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');

    // delete first face picture
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(2);
    const pictures = res.body.user.pictures;

    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 0)
      .query({ id: pictures[0] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });

  it('delete no face picture', async () => {
    // add picture without face
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(2);
    const pictures = res.body.user.pictures;

    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 0)
      .query({ id: pictures[1] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });

  it('edit profile picture - replace matching with matching', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(1);
    const pictures = res.body.user.pictures;

    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 0)
      .query({ id: pictures[0] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });

  it('edit profile picture - replace matching with not matching', async () => {
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(1);
    const pictures = res.body.user.pictures;

    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 0)
      .query({ id: pictures[0] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('reverifying');
  });

});

describe('new reverification', () => {
  const challengeId = '1110f6e4-a225-11ed-bf55-d33b0802ecdc';

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.22', locale: 'en' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { support: true };
    res = await user.save();

    // start
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            id: challengeId,
            imageWidth: 1000,
            imageHeight: 1000,
            areaLeft: 218,
            areaTop: 125,
            areaWidth: 562,
            areaHeight: 750,
            minFaceAreaPercent: 50,
            noseLeft: 412,
            noseTop: 525,
            noseWidth: 20,
            noseHeight: 20,
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/start')
      .set('authorization', 0)
      .send({ imageWidth: 1000, imageHeight: 1000 });
    expect(res.status).to.equal(200);

    // post frames
    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    // verify
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: true
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');
  });

  it('add picture that matches', async () => {
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/user/verificationStatus')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');
    expect(res.body.rejectionReason).to.equal();
  });

  it('add picture that does not matches', async () => {
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ UnmatchedFaces: [ {} ] });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/user/verificationStatus')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');
    expect(res.body.rejectionReason).to.equal();
  });

  it('delete face picture none remaining', async () => {
    // add picture without face
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');

    // delete first face picture
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(2);
    const pictures = res.body.user.pictures;

    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 0)
      .query({ id: pictures[0] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('rejected');
  });

  it('delete face picture some remaining', async () => {
    // add picture with face
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');

    // delete first face picture
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(2);
    const pictures = res.body.user.pictures;

    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 0)
      .query({ id: pictures[0] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });

  it('delete no face picture', async () => {
    // add picture without face
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(2);
    const pictures = res.body.user.pictures;

    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 0)
      .query({ id: pictures[1] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });

  it('edit profile picture - replace matching with matching', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(1);
    const pictures = res.body.user.pictures;

    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 0)
      .query({ id: pictures[0] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });

  it('edit profile picture - replace matching with not matching', async () => {
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(1);
    const pictures = res.body.user.pictures;

    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 0)
      .query({ id: pictures[0] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verificationStatus).to.equal('rejected');
  });

});

it('transition to new reverification', async () => {
  const challengeId = '1110f6e4-a225-11ed-bf55-d33b0802ecdc';

  // start with old system
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  // add two pitures
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 0)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 0)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  // start
  fakeLambda.invoke = function (params) {
    const impl = function (resolve, reject) {
      resolve({
        Payload: JSON.stringify({
          id: challengeId,
          imageWidth: 1000,
          imageHeight: 1000,
          areaLeft: 218,
          areaTop: 125,
          areaWidth: 562,
          areaHeight: 750,
          minFaceAreaPercent: 50,
          noseLeft: 412,
          noseTop: 525,
          noseWidth: 20,
          noseHeight: 20,
        }),
      });
    };
    return {
      promise: () => new Promise(impl),
    };
  };

  res = await request(app)
    .post('/v1/liveness/start')
    .set('authorization', 0)
    .send({ imageWidth: 1000, imageHeight: 1000 });
  expect(res.status).to.equal(200);

  // post frames
  res = await request(app)
    .post('/v1/liveness/frame')
    .set('authorization', 0)
    .attach('image', validImagePath)
    .query({ challengeId });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/liveness/frame')
    .set('authorization', 0)
    .attach('image', validImagePath)
    .query({ challengeId });
  expect(res.status).to.equal(200);

  // verify
  fakeLambda.invoke = function (params) {
    const impl = function (resolve, reject) {
      resolve({
        Payload: JSON.stringify({
          success: true
        }),
      });
    };
    return {
      promise: () => new Promise(impl),
    };
  };

  res = await request(app)
    .post('/v1/liveness/verify')
    .set('authorization', 0)
    .send({ challengeId });
  expect(res.status).to.equal(200);
  expect(res.body.verificationStatus).to.equal('verified');

  // update to new reverification system
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.22', locale: 'en' });
  expect(res.status).to.equal(200);

  // mock face no match
  fakeRekognition.compareFaces = function (params) {
    const impl = function (resolve, reject) {
      resolve({ FaceMatches: [{}], UnmatchedFaces: [ {} ] });
    };
    return {
      promise: () => new Promise(impl),
    };
  };

  // purge cached result
  user = await User.findById('0');
  user.verification.faceComparisonResults = [];
  await user.save();

  // delete second picture should trigger reverification that is rejected
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.pictures.length).to.equal(2);
  const pictures = res.body.user.pictures;

  res = await request(app)
    .delete('/v1/user/picture')
    .set('authorization', 0)
    .query({ id: pictures[1] });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.verificationStatus).to.equal('rejected');
  expect(res.body.user.rejectionReason).to.equal('Make sure your first profile picture is a picture of you, and only you.');

  // mock face match
  fakeRekognition.compareFaces = function (params) {
    const impl = function (resolve, reject) {
      resolve({ FaceMatches: [ {} ] });
    };
    return {
      promise: () => new Promise(impl),
    };
  };

  // edit 1st picture should trigger reverification that is approved
  res = await request(app)
    .post('/v1/user/editPicture')
    .set('authorization', 0)
    .query({ id: pictures[0] })
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.verificationStatus).to.equal('verified');

  // add new picture should not trigger reverifying now that user is in new system
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 0)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);
});


it('handle JPG file extension', async () => {
  const challengeId = '1110f6e4-a225-11ed-bf55-d33b0802ecdc';

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.22', locale: 'en' });
  expect(res.status).to.equal(200);

  buffer = 'a'.repeat(100);
  JPG = temp.openSync({ suffix: '.JPG' }).path;
  fs.writeFileSync(JPG, buffer);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 0)
    .attach('image', JPG);
  expect(res.status).to.equal(200);

  // start
  fakeLambda.invoke = function (params) {
    const impl = function (resolve, reject) {
      resolve({
        Payload: JSON.stringify({
          id: challengeId,
          imageWidth: 1000,
          imageHeight: 1000,
          areaLeft: 218,
          areaTop: 125,
          areaWidth: 562,
          areaHeight: 750,
          minFaceAreaPercent: 50,
          noseLeft: 412,
          noseTop: 525,
          noseWidth: 20,
          noseHeight: 20,
        }),
      });
    };
    return {
      promise: () => new Promise(impl),
    };
  };

  res = await request(app)
    .post('/v1/liveness/start')
    .set('authorization', 0)
    .send({ imageWidth: 1000, imageHeight: 1000 });
  expect(res.status).to.equal(200);

  // post frames
  res = await request(app)
    .post('/v1/liveness/frame')
    .set('authorization', 0)
    .attach('image', validImagePath)
    .query({ challengeId });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/liveness/frame')
    .set('authorization', 0)
    .attach('image', validImagePath)
    .query({ challengeId });
  expect(res.status).to.equal(200);

  // verify
  fakeLambda.invoke = function (params) {
    const impl = function (resolve, reject) {
      resolve({
        Payload: JSON.stringify({
          success: true
        }),
      });
    };
    return {
      promise: () => new Promise(impl),
    };
  };

  res = await request(app)
    .post('/v1/liveness/verify')
    .set('authorization', 0)
    .send({ challengeId });
  expect(res.status).to.equal(200);
  expect(res.body.verificationStatus).to.equal('verified');
});
