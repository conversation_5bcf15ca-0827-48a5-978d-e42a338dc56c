const { app, validImagePath } = require('./common');
const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const moment = require('moment');
const sinon = require('sinon');
const { DateTime } = require('luxon');
const basic = require('../lib/basic');
const iapHelper = require('./helper/iap');
const NeuronPurchaseReceipt = require('../models/neuron-purchase-receipt');
const User = require('../models/user');
const Question = require('../models/question');
const OpenaiTransaction = require('../models/openai-transaction');
const openaiClient = require('../lib/openai-client');
const { fakeOpenaiClient } = require('./stub');
const { createQuestion } = require('../lib/social');
const promptsLib = require('../lib/prompts');
const replicateClient = require('../lib/replicate-client');
const groqClient = require('../lib/groq-client');
const { BOO_SUPPORT_ID } = require('../lib/chat');
const { OpenAI } = require('../lib/prompt');
const UsersProfileAnalysisPrompt = require('../models/users-profile-analysis-prompts');
const { getChatAnalysisResultForYourTurn } = require('../lib/openai');

const stubAiClients = (output) => {
  // Fake clients with chat completion methods
  const fakeAiClient = { chat: { completions: { create: () => {} } } };

  // Stub the getClient methods to return fake clients
  sinon.stub(groqClient, 'getGroqApiClient').returns(fakeAiClient);
  sinon.stub(openaiClient, 'getOpenaiClient').returns(fakeAiClient);

  const outputText = `I'm happy to help you analyze your match's profile! Unfortunately, it seems that their profile doesn't provide much information about their personality type, age, gender, or zodiac sign. However, I can still offer some general insights on how to approach them based on their bio and interests.\n\nHere are four concise insights into their likes, dislikes, and how to best connect with them:\n\n* **Get creative with conversations**: Since their profile is minimal, it's likely they're not looking for a generic, small-talk-filled chat. Try to spark a creative conversation that showcases your personality and interests. This will give them a reason to engage with you and stand out from other matches.\n* **Find common ground through shared interests**: Take note of their interests and hobbies listed in their bio. Use these as conversation starters to build a connection. Ask open-ended questions about their favorite activities or how they got into a particular hobby. This will show you're genuinely interested in getting to know them.\n* **Be yourself, but be respectful**: Without much information, it's hard to gauge their personality type or preferences. Keep your initial messages light-hearted and respectful. Avoid coming on too strong or being too pushy. Keep the tone friendly and casual, and let the conversation evolve naturally.\n* **Ask thought-provoking questions**: To keep the conversation engaging, ask questions that encourage them to share more about themselves. This could be about their favorite books, travel experiences, or goals. Listen actively and respond thoughtfully to build a meaningful connection.\n\nRemember, the goal is to start a conversation, not to interview or interrogate them. Be natural, be respectful, and be yourself!`;

  const outputJSON = `{ \"output\": [\"So, Stephanie, have you traveled to any new gaming conventions recently?\", \"What's your go-to gaming genre when you're exploring new travel destinations?\", \"If you could choose any gaming-themed getaway, where would you go and what would you play?\", \"Are there any travel-inspired games that you're obsessed with right now, Stephanie?\"] }`;
  const singleElementJSON = `{ \"output\": [\"So, Stephanie, have you traveled to any new gaming conventions recently?\"] }`;

  const createResponse = (id, content, usage, includeXGroq = false) => {
    const choiceKey = includeXGroq ? 'delta' : 'message';
    const response = {
      id,
      usage: {
        prompt_tokens: usage.prompt_tokens,
        completion_tokens: usage.prompt_tokens,
      },
      choices: [
        {
          [choiceKey]: {
            content: output ? JSON.stringify({ output }) : content,
          },
          finish_reason: 'stop',
        },
      ],
    };
    if (includeXGroq) {
      response.x_groq = {
        usage: {
          prompt_tokens: usage.prompt_tokens,
          completion_tokens: usage.prompt_tokens,
        },
      };
    }
    return response;
  };

  const createStreamResponse = async function* () {
    yield createResponse(
      'chatcmpl-42ba5654-c988-4217-a0a0-65824eee3dca',
      outputText,
      { prompt_tokens: 10, completion_tokens: 10 },
      true,
    );
  };

  return sinon.stub(fakeAiClient.chat.completions, 'create').callsFake(async (params) => {
    let prompt;
    if (params.model?.includes('gpt')) {
      prompt = params.messages[0]?.content[0]?.text;
    } else {
      prompt = params.messages?.length > 1
        ? params.messages[1]?.content
        : params.messages[0]?.content;
    }
    if (prompt?.includes('sexual favors')) {
      return Promise.reject(new Error('cannot create content that requests or implies sexual favors'));
    }
    if (params.stream) {
      return Promise.resolve(createStreamResponse());
    }
    return Promise.resolve(
      createResponse(
        'chatcmpl-42ba5654-c988-4217-a0a0-65824eee3dca',
        params.response_format?.type === 'json_object'
          ? params.messages[0]?.content[0]?.text.startsWith("Proofread") ? singleElementJSON : outputJSON
          : outputText,
        { prompt_tokens: 10, completion_tokens: 10 },
      ),
    );
  });
};

const generatePayloadMessage = (requestPayload, prompt, model, isJson) => {
  if (model.includes('gpt')) {
    // OpenAI is being used
    requestPayload.messages = [
      {
        role: "user",
        content: [{
          type: "text",
          text: prompt,
        }],
      },
    ];
  }
  if (model.includes('llama3-70b-8192')) {
    // Groq is being used
    const systemMessage = {
      role: 'system',
      content: 'You are a helpful assistant who provides all responses in JSON format',
    };
    requestPayload.messages = [
      {
        role: 'user',
        content: prompt,
      },
    ];
    if (isJson) {
      requestPayload.messages.unshift(systemMessage);
    }
  }
};

describe('testing ice breaker route for Portuguese localisation', async () => {
  let callStub;
  const expected = [
    'Vi que você gosta de cozinhar! Qual é o prato mais interessante que você já preparou?',
    'Você tem alguma música ou banda favorita que sempre te anima?',
    'Adoro viajar. Qual foi o destino mais incrível que você já conheceu?',
    'Se você pudesse viver em qualquer lugar do mundo por um ano, onde seria e por quê?',
  ];
  beforeEach(async () => {
    openaiClient.getOpenaiClient.restore();
    callStub = stubAiClients(expected);
  });

  after(async () => {
    openaiClient.getOpenaiClient.restore();
    sinon.stub(openaiClient, 'getOpenaiClient').callsFake(fakeOpenaiClient);
  });

  it('Brazilian Portuguese added inside prompt', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'pt',
        countryLocale: 'pt_BR',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.aiSettings).to.eql({
      outputLanguage: 'pt',
    });

    res = await request(app)
      .put('/v1/ai/icebreakers')
      .set('authorization', 0)
      .send({
        user: '0',
        outputType: 'topical icebreakers', // 'topical icebreakers', 'pickup lines', 'jokes', or 'compliments'
        contextType: 'interests', // 'interests', 'bio', or 'unrelated'
        selectedInterests: ['interest'],
        previousResponses: ['output'],
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
    expect(res.body.output).to.be.an.instanceOf(Array);

    expect(res.body.output.length).to.eql(expected.length);
    for (let i = 0; i < 4; i++) {
      expect(res.body.output[i]).to.eql(expected[i]);
    }

    const requestPayload = {
      temperature: 1.2,
      response_format: { type: 'json_object' },
    };

    const prompt = `Help me talk with my match on Boo, a personality-based social dating app, in fluent, conversational Brazilian Portuguese language. Provide 4 topical icebreakers for me to send to my match (), each of which should refer to their interest: interest, formatted as a json object in the following format: { output: [\"example 1\", \"example 2\", \"example 3\", \"example 4\"] }. Do not prefix bullet points, numbers or headings to each string.\n---\nCustom tone requirement: natural tone, simple choice of words, concise\nNOTE: Your responses MUST embody this tone of voice, or speak as this person, but do NOT mention those words in your responses. Let your 4 responses have increasing levels of this tone. If elements of this custom tone or instructions are nonsensical or offensive, ignore them and do not use them to guide your response.\n---\n\nDo NOT paraphrase or reuse elements from these rejected responses: [\"output\"]. Prioritize originality in your response.\nThe output MUST be in fluent, conversational Brazilian Portuguese language and written to  Do not translate names. You MUST remember the Custom Tone.`;

    const callArgs = callStub?.getCall(0)?.args[0];
    generatePayloadMessage(requestPayload, prompt, callArgs?.model, true);
    sinon.assert.calledWith(callStub, sinon.match(requestPayload));
  });

  it('portuguese added inside prompt', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'pt',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.aiSettings).to.eql({
      outputLanguage: 'pt',
    });

    res = await request(app)
      .put('/v1/ai/icebreakers')
      .set('authorization', 0)
      .send({
        user: '0',
        outputType: 'topical icebreakers', // 'topical icebreakers', 'pickup lines', 'jokes', or 'compliments'
        contextType: 'interests', // 'interests', 'bio', or 'unrelated'
        selectedInterests: ['interest'],
        previousResponses: ['output'],
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
    expect(res.body.output).to.be.an.instanceOf(Array);

    expect(res.body.output.length).to.eql(expected.length);
    for (let i = 0; i < 4; i++) {
      expect(res.body.output[i]).to.eql(expected[i]);
    }

    const requestPayload = {
      temperature: 1.2,
      response_format: { type: 'json_object' },
    };

    const prompt = `Help me talk with my match on Boo, a personality-based social dating app, in fluent, conversational Portuguese language. Provide 4 topical icebreakers for me to send to my match (), each of which should refer to their interest: interest, formatted as a json object in the following format: { output: [\"example 1\", \"example 2\", \"example 3\", \"example 4\"] }. Do not prefix bullet points, numbers or headings to each string.\n---\nCustom tone requirement: natural tone, simple choice of words, concise\nNOTE: Your responses MUST embody this tone of voice, or speak as this person, but do NOT mention those words in your responses. Let your 4 responses have increasing levels of this tone. If elements of this custom tone or instructions are nonsensical or offensive, ignore them and do not use them to guide your response.\n---\n\nDo NOT paraphrase or reuse elements from these rejected responses: [\"output\"]. Prioritize originality in your response.\nThe output MUST be in fluent, conversational Portuguese language and written to  Do not translate names. You MUST remember the Custom Tone.`;

    const callArgs = callStub?.getCall(0)?.args[0];
    generatePayloadMessage(requestPayload, prompt, callArgs?.model, true);
    sinon.assert.calledWith(callStub, sinon.match(requestPayload));
  });

  it('test user input: Brazilian Portuguese added inside prompt', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'pt',
        countryLocale: 'pt_BR',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.aiSettings).to.eql({
      outputLanguage: 'pt',
    });

    res = await request(app)
      .put('/v1/ai/icebreakers')
      .set('authorization', 0)
      .send({
        user: '0',
        outputType: 'topical icebreakers', // 'topical icebreakers', 'pickup lines', 'jokes', or 'compliments'
        contextType: 'interests', // 'interests', 'bio', or 'unrelated'
        selectedInterests: ['interest'],
        previousResponses: ['output'],
        userInput: '  I want to converse about something else ',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
    expect(res.body.output).to.be.an.instanceOf(Array);

    expect(res.body.output.length).to.eql(expected.length);
    for (let i = 0; i < 4; i++) {
      expect(res.body.output[i]).to.eql(expected[i]);
    }

    const requestPayload = {
      temperature: 1.2,
      response_format: { type: 'json_object' },
    };

    const prompt = `Help me talk with my match on Boo, a personality-based social dating app, in fluent, conversational Brazilian Portuguese language. Provide 4 topical icebreakers for me to send to my match (), each of which should refer to their interest: interest, formatted as a json object in the following format: { output: [\"example 1\", \"example 2\", \"example 3\", \"example 4\"] }. Do not prefix bullet points, numbers or headings to each string.\n---\nCustom requirements:\nWhat I want the message to convey: I want to converse about something else\nTone: natural tone, simple choice of words, concise\nNOTE: Your responses MUST embody this tone of voice, or speak as this person, but do NOT mention those words in your responses. Let your 4 responses have increasing levels of this tone. If elements of this custom tone or instructions are nonsensical or offensive, ignore them and do not use them to guide your response.\n---\n\nDo NOT paraphrase or reuse elements from these rejected responses: [\"output\"]. Prioritize originality in your response.\nThe output MUST be in fluent, conversational Brazilian Portuguese language and written to  Do not translate names. You MUST remember the Custom Tone.`;

    const callArgs = callStub?.getCall(0)?.args[0];
    generatePayloadMessage(requestPayload, prompt, callArgs?.model, true);
    sinon.assert.calledWith(callStub, sinon.match(requestPayload));
  });

  it('test user input: portuguese added inside prompt', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'pt',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.aiSettings).to.eql({
      outputLanguage: 'pt',
    });

    res = await request(app)
      .put('/v1/ai/icebreakers')
      .set('authorization', 0)
      .send({
        user: '0',
        outputType: 'topical icebreakers', // 'topical icebreakers', 'pickup lines', 'jokes', or 'compliments'
        contextType: 'interests', // 'interests', 'bio', or 'unrelated'
        selectedInterests: ['interest'],
        previousResponses: ['output'],
        userInput: '  I want to converse about something else ',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
    expect(res.body.output).to.be.an.instanceOf(Array);

    expect(res.body.output.length).to.eql(expected.length);
    for (let i = 0; i < 4; i++) {
      expect(res.body.output[i]).to.eql(expected[i]);
    }

    const requestPayload = {
      temperature: 1.2,
      response_format: { type: 'json_object' },
    };

    const prompt = `Help me talk with my match on Boo, a personality-based social dating app, in fluent, conversational Portuguese language. Provide 4 topical icebreakers for me to send to my match (), each of which should refer to their interest: interest, formatted as a json object in the following format: { output: [\"example 1\", \"example 2\", \"example 3\", \"example 4\"] }. Do not prefix bullet points, numbers or headings to each string.\n---\nCustom requirements:\nWhat I want the message to convey: I want to converse about something else\nTone: natural tone, simple choice of words, concise\nNOTE: Your responses MUST embody this tone of voice, or speak as this person, but do NOT mention those words in your responses. Let your 4 responses have increasing levels of this tone. If elements of this custom tone or instructions are nonsensical or offensive, ignore them and do not use them to guide your response.\n---\n\nDo NOT paraphrase or reuse elements from these rejected responses: [\"output\"]. Prioritize originality in your response.\nThe output MUST be in fluent, conversational Portuguese language and written to  Do not translate names. You MUST remember the Custom Tone.`;

    const callArgs = callStub?.getCall(0)?.args[0];
    generatePayloadMessage(requestPayload, prompt, callArgs?.model, true);
    sinon.assert.calledWith(callStub, sinon.match(requestPayload));
  });
});

describe('testing ice breaker route with object type response from groq llama3', async () => {
  const expected = [
    "So, Stephanie, have you traveled to any new gaming conventions recently?",
    "What's your go-to gaming genre when you're exploring new travel destinations?",
    "If you could choose any gaming-themed getaway, where would you go and what would you play?",
    "Are there any travel-inspired games that you're obsessed with right now, Stephanie?"];
  beforeEach(async () => {
    // Restore original clients if previously stubbed
    openaiClient.getOpenaiClient.restore();
    stubAiClients(expected);
  });

  after(async () => {
    openaiClient.getOpenaiClient.restore();
    sinon.stub(openaiClient, 'getOpenaiClient').callsFake(fakeOpenaiClient);
  });

  it('testing route PUT /v1/ai/icebreakers', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'en',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.aiSettings).to.eql({
      outputLanguage: 'en',
    });
    res = await request(app)
      .put('/v1/ai/icebreakers')
      .set('authorization', 0)
      .send({
        user: '0',
        outputType: 'topical icebreakers', // 'topical icebreakers', 'pickup lines', 'jokes', or 'compliments'
        contextType: 'interests', // 'interests', 'bio', or 'unrelated'
        selectedInterests: ['interest'],
        previousResponses: ['output'],
      });

    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
    expect(res.body.output).to.be.an.instanceOf(Array);
    expect(res.body.output.length).to.eql(expected.length);
    for (let i = 0; i < 4; i++) {
      expect(res.body.output[i]).to.eql(expected[i]);
    }
  });
});

describe('testing routes with groq llama3', async () => {
  beforeEach(async () => {
    openaiClient.getOpenaiClient.restore();
    stubAiClients();
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'en',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.aiSettings).to.eql({
      outputLanguage: 'en',
    });
  });

  after(async () => {
    openaiClient.getOpenaiClient.restore();
    sinon.stub(openaiClient, 'getOpenaiClient').callsFake(fakeOpenaiClient);
  });

  it('testing route PUT /v1/ai/profileAnalysis', async () => {
    const res = await request(app)
      .put('/v1/ai/profileAnalysis')
      .set('authorization', 0)
      .send({
        user: '0',
        outputType: 'profile', // 'profile' or 'compatibility'
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
    let expected = `I'm happy to help you analyze your match's profile! Unfortunately, it seems that their profile doesn't provide much information about their personality type, age, gender, or zodiac sign. However, I can still offer some general insights on how to approach them based on their bio and interests.\n\nHere are four concise insights into their likes, dislikes, and how to best connect with them:\n\n* **Get creative with conversations**: Since their profile is minimal, it's likely they're not looking for a generic, small-talk-filled chat. Try to spark a creative conversation that showcases your personality and interests. This will give them a reason to engage with you and stand out from other matches.\n* **Find common ground through shared interests**: Take note of their interests and hobbies listed in their bio. Use these as conversation starters to build a connection. Ask open-ended questions about their favorite activities or how they got into a particular hobby. This will show you're genuinely interested in getting to know them.\n* **Be yourself, but be respectful**: Without much information, it's hard to gauge their personality type or preferences. Keep your initial messages light-hearted and respectful. Avoid coming on too strong or being too pushy. Keep the tone friendly and casual, and let the conversation evolve naturally.\n* **Ask thought-provoking questions**: To keep the conversation engaging, ask questions that encourage them to share more about themselves. This could be about their favorite books, travel experiences, or goals. Listen actively and respond thoughtfully to build a meaningful connection.\n\nRemember, the goal is to start a conversation, not to interview or interrogate them. Be natural, be respectful, and be yourself!`;
    expected = expected
      .replace(/(^|[^\n])\n(?!\n)/g, '$1\n\n')
      .replaceAll('**', '');
    expect(res.body.output).to.equal(expected);
  });

  it('testing route PUT /v1/ai/bio/generate', async () => {
    const res = await request(app)
      .put('/v1/ai/bio/generate')
      .set('authorization', 0)
      .send({
        context: {
          gender: true,
          age: true,
          mbti: true,
          location: true,
          languages: true,
          education: true,
          work: true,
          interests: true,
          lookingFor: true,
        },
        content: 'text',
        tone: 'text',
        previousResponses: ['output'],
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
    expect(res.body.output).to.be.an.instanceOf(Array);
  });

  it('creates a question and test route PUT /v1/ai/social with that question', async () => {
    const question = await createQuestion({
      createdAt: Date.now() + 90 * 24 * 60 * 60 * 1000, // 3 months
      text: 'question 3',
      interestName: 'questions',
    });

    const res = await request(app)
      .put('/v1/ai/social')
      .set('authorization', 0)
      .send({
        questionId: question._id,
        // commentId: 'id', // optional if responding to a comment
        outputType: 'suggest', // 'suggest' or 'paraphrase' or 'proofread'
        userInput: 'text',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
  });
});

describe('testing route PUT /v1/ai/chatAnalysis && /v1/ai/continueConversation with groq llama3', async () => {
  beforeEach(async () => {
    openaiClient.getOpenaiClient.restore();
    stubAiClients();
    for (let uid = 0; uid < 2; uid++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({
          appVersion: '1.13.17',
          locale: 'en',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/education')
        .set('authorization', uid)
        .send({
          education: `education${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/description')
        .set('authorization', uid)
        .send({
          description: `description${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/work')
        .set('authorization', uid)
        .send({
          work: `work${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .get('/v1/user/quizQuestions')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      const answers = Object.keys(res.body.questions).reduce((map, id) => {
        map[id] = 0;
        return map;
      }, {});
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({ answers });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/handle')
        .set('authorization', uid)
        .send({
          handle: `handle${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          purpose: ['friends'],
        });
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: new Date().getFullYear() - 31,
          month: 1,
          day: 1,
        });
      // Honolulu, HI
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          latitude: 21.3,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/profilePromptAnswers')
        .set('authorization', uid)
        .send({
          prompts: [{ id: promptsLib.promptsArray[0].id, answer: `${uid}` }],
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/approveAllFollowers')
        .set('authorization', uid)
        .send({ approveAllFollowers: false });
      expect(res.status).to.equal(200);
      // mock upload two pictures
      const user = await User.findOne({ _id: uid });
      user.pictures.push('picture0');
      user.pictures.push('picture1');
      res = await user.save();
    }

    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    for (let i = 0; i < 8; i++) {
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 1)
        .send({
          user: '0',
          text: `hi ${i}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 0)
        .send({
          user: '1',
          text: `hello ${i}`,
        });
      expect(res.status).to.equal(200);
    }
  });

  after(async () => {
    openaiClient.getOpenaiClient.restore();
    sinon.stub(openaiClient, 'getOpenaiClient').callsFake(fakeOpenaiClient);
  });

  it('testing PUT /v1/ai/chatAnalysis', async () => {
    const res = await request(app)
      .put('/v1/ai/chatAnalysis')
      .set('authorization', 0)
      .send({
        user: '1',
        outputType: 'sentiment', // 'sentiment', 'performance', or 'intent'
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
  });

  it('test userInput in route /v1/ai/chatAnalysis', async () => {
    const res = await request(app)
      .put('/v1/ai/chatAnalysis')
      .set('authorization', 0)
      .send({
        user: '1',
        outputType: 'sentiment', // 'sentiment', 'performance', or 'intent'
        userInput: 'some text',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
  });

  it('testing route PUT /v1/ai/continueConversation', async () => {
    const res = await request(app)
      .put('/v1/ai/continueConversation')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
  });

  it('test userInput in route /v1/ai/continueConversation', async () => {
    const res = await request(app)
      .put('/v1/ai/continueConversation')
      .set('authorization', 0)
      .send({
        user: '1',
        userInput: '  I want to converse about something else  ',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
  });
});

describe('testing bio routes', async () => {
  beforeEach(async () => {
    openaiClient.getOpenaiClient.restore();
    stubAiClients();
  });
  after(async () => {
    openaiClient.getOpenaiClient.restore();
    sinon.stub(openaiClient, 'getOpenaiClient').callsFake(fakeOpenaiClient);
  });

  it('testing route PUT /v1/ai/bio/improve', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'en',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.aiSettings).to.eql({
      outputLanguage: 'en',
    });

    const requestPayload = {
      bio: `Rumblings of thunder, flashes of lightning – that's just my personality shining through! As an ESFJ with a flair for the dramatic, I live for making memories with friends, trying new foods, and marathoning the latest anime episodes. Join me, and let's create an unforgettable storm of excitement!`,
      context: {
        gender: true,
        age: true,
        mbti: true,
        location: false,
        languages: true,
        education: true,
        work: true,
        interests: true,
        lookingFor: true,
      },
      content: `Add length and add more my interest context into my profile`,
      previousResponses: [
        "As a spark plug of energy, I light up rooms with my ESFJ charm and infectious laughter. When I'm not trying out new recipes in the kitchen or belting out my favorite tunes, you can find me cozied up with a good book or planning my next adventure. Looking for someone to share in the excitement and create unforgettable memories!",
        "Warning: sassy Indonesian girl with a flair for the dramatic ahead! By day, I'm a university student and language enthusiast, but by night, I transform into a gaming, anime-binging, meme-loving machine. If you can keep up with my quick wit and love for all things spooky, let's grab a plate of nasi goreng and see where the night takes us!",
        "Musik, makanan, and a side of laughter – that's my recipe for a perfect night in. As a 24-year-old ESFJ with a passion for creativity, you can find me experimenting with new recipes in the kitchen, strumming my guitar, or planning my next outdoor escapade. If you're looking for a partner in crime who shares your love for life's simple pleasures, let's connect!",
        "Get ready for a whirlwind of excitement with this ESFJ firecracker! By day, I'm a psychology enthusiast and tech-savvy student, but by night, I transform into a horror movie aficionado and binge-watching pro. If you can keep up with my quick wit, love for animals, and passion for trying new things, let's create a storm of laughter and adventure together!",
      ],
    };
    res = await request(app)
      .put('/v1/ai/bio/improve')
      .set('authorization', 0)
      .send(requestPayload);
    expect(res.status).to.equal(200);
  });

  it('testing route PUT /v1/ai/bio/proofread', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'en',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.aiSettings).to.eql({
      outputLanguage: 'en',
    });

    const requestPayload = {
      bio: "In the vibrant city of life, I'm a spark of energy, always on the lookout for someone to share in the excitement of discovery. Whether it's trying new recipes, exploring hidden gems, or gaming into the night, I'm a 24-year-old ESFJ with a passion for creativity and a thirst for life. If your heart beats to the rhythm of laughter and excitement, let's harmonize our frequencies and create a symphony of unforgettable moments!",
      previousResponses: [],
    };
    res = await request(app)
      .put('/v1/ai/bio/proofread')
      .set('authorization', 0)
      .send(requestPayload);
    expect(res.status).to.equal(200);
    const transaction = await OpenaiTransaction.findOne({ user: 0 }).lean();
    expect(transaction).to.not.have.property('payloadOnError');
  });

  it('testing payloadOnError in OpenaiTransaction', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'en',
      });
    expect(res.status).to.equal(200);
    const requestPayload = {
      context: {
        gender: true,
        age: true,
        mbti: true,
        location: true,
        languages: true,
        education: true,
        work: true,
        interests: true,
        lookingFor: true,
      },
      content: 'sexual favors',
    };
    res = await request(app)
      .put('/v1/ai/bio/generate')
      .set('authorization', 0)
      .send(requestPayload);
    const transaction = await OpenaiTransaction.findOne({ user: 0 }).lean();
    expect(transaction).to.have.property('payloadOnError');
  });
});

describe('testing make neurons free', async () => {
  beforeEach(async () => {
    openaiClient.getOpenaiClient.restore();
    stubAiClients();
  });

  after(async () => {
    openaiClient.getOpenaiClient.restore();
    sinon.stub(openaiClient, 'getOpenaiClient').callsFake(fakeOpenaiClient);
  });
  it('should deduct neurons when app_291 is false', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    let user = await User.findOne({ _id: 0 });
    const numBooAINeurons = user.numBooAINeurons;

    res = await request(app)
      .put('/v1/ai/icebreakers')
      .set('authorization', 0)
      .send({
        user: '0',
        outputType: 'topical icebreakers',
        contextType: 'interests',
        selectedInterests: ['interest'],
      });
    expect(res.status).to.equal(200);
    const neuronsAfter = res.body.numBooAINeurons;

    res = await request(app)
      .get('/v1/ai/prices')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const price = res.body.prices.icebreakers;
    user = await User.findOne({ _id: 0 });
    expect(user.numBooAINeurons).to.equal(numBooAINeurons - price);
    expect(neuronsAfter).to.equal(user.numBooAINeurons);
  });

  it('should not deduct neurons for app_291 true', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.50',
      });
    expect(res.status).to.equal(200);

    const user = await User.findOne({ _id: 0 });
    const numBooAINeurons = user.numBooAINeurons;

    res = await request(app)
      .put('/v1/ai/icebreakers')
      .set('authorization', 0)
      .send({
        user: '0',
        outputType: 'topical icebreakers',
        contextType: 'interests',
        selectedInterests: ['interest'],
      });
    expect(res.status).to.equal(200);
    expect(res.body.numBooAINeurons).to.equal(numBooAINeurons);
  });
});

describe('testing user info injection in PUT /v1/ai/social', async () => {
  let callStub;
  beforeEach(async () => {
    openaiClient.getOpenaiClient.restore();
    callStub = stubAiClients();
  });
  after(async () => {
    openaiClient.getOpenaiClient.restore();
    sinon.stub(openaiClient, 'getOpenaiClient').callsFake(fakeOpenaiClient);
  });
  it('test user info not injected on prompt if user is banned', async () => {
    for (let i = 0; i < 3; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      const user = await User.findById(i);
      user.firstName = `name ${i}`;
      user.gender = 'male';
      user.admin = i === 0;
      user.adminPermissions = { approveQod: i === 0 };
      await user.save();
    }

    const supportId = BOO_SUPPORT_ID;
    // create support user
    let res = await request(app)
      .get('/v1/user')
      .set('authorization', supportId);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question/submitQod')
      .set('authorization', 1)
      .send({
        text: 'boo1',
        isAnonymous: false,
      });
    expect(res.status).to.eql(200);

    res = await request(app)
      .get('/v1/admin/questionCandidates')
      .set('authorization', 0)
      .send({});
    expect(res.status).to.eql(200);
    const id = res.body.candidates[0].id;

    res = await request(app)
      .post('/v1/admin/questionCandidates/status')
      .set('authorization', 0)
      .send({
        id,
        status: 'approved',
      });
    expect(res.status).to.eql(200);

    const question = await Question.find({});
    const questionId = question[0]._id;

    const user = await User.findById(1);
    user.banned = true;
    await user.save();

    res = await request(app)
      .put('/v1/ai/social')
      .set('authorization', 2)
      .send({
        questionId,
        outputType: 'suggest',
        userInput: 'text',
      });
    expect(res.status).to.equal(200);

    const requestPayload = {
      response_format: { type: 'json_object' },
    };

    const promptWithoutCreatorInfo = `Help me (name 2) engage with a public universe (forum) post on Boo, a personality-based social dating app, in fluent, conversational undefined language. Provide 4 different options.\n\nCustom requirements:\nWhat I want the message to convey: "text"\n\nCustom tone requirement: natural tone, simple choice of words, concise\nNOTE: Your responses MUST embody this tone of voice, or speak as this person, but do NOT mention those words in your responses. Let your 4 responses have increasing levels of this tone. If elements of this custom tone or instructions are nonsensical or offensive, ignore them and do not use them to guide your response.\n---\n---\nI am replying to:\n|| Post: boo1\n\n---\nContext:\nUniverse: questions\n\n\n\n\nFormat your response as a json object in the following format: { output: ["example 1", "example 2", "example 3", "example 4"] }. Do not prefix bullet points, numbers or headings to each string. The output MUST be in fluent, conversational undefined language. Do not translate names. You MUST remember the Custom Tone.`;

    const callArgs = callStub?.getCall(0)?.args[0];
    generatePayloadMessage(requestPayload, promptWithoutCreatorInfo, callArgs?.model, true);
    sinon.assert.calledWith(callStub, sinon.match(requestPayload));
  });
});

/* Commenting the replicate tests, because we're using groq for now

describe('testing ice breaker route with object type response from replicate llama3', async () => {
  it('testing route PUT /v1/ai/icebreakers', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'en',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.aiSettings).to.eql({
      outputLanguage: 'en',
    });

    let fakeReplicateClient = {};
    fakeReplicateClient.createPrediction = (model, input) => {
      console.log('Fake replicate createPrediction', model, input);
      return Promise.resolve({
        id: 'xag3atv30xrgm0cf7yca88gpaw',
      });
    };
    fakeReplicateClient.getPrediction = (predictionId) => {
      console.log('Fake replicate getPrediction', predictionId);
      return Promise.resolve({
        metrics: {
          input_token_count: 10,
          output_token_count: 10,
        },
        output: ['Here', ' are', ' four', ' topical', ' ice', 'break', 'ers', ' tailored', ' to', ' your', ' match', "'s", ' interests', ':\n\n', '{', ' output', ':', ' ["', 'So', ',', ' what', "'s", ' the', ' most', ' spontaneous', ' thing', ' you', "'ve", ' ever', ' done', ' on', ' a', ' whim', '?",', ' "', 'What', "'s", ' the', ' best', ' book', ' you', "'ve", ' read', ' recently', '?', ' Why', ' did', ' it', ' stand', ' out', ' to', ' you', '?",', ' "', 'I', ' saw', ' you', "'re", ' into', ' hiking', '!', ' What', "'s", ' your', ' favorite', ' trail', ' you', "'ve", ' explored', ' so', ' far', '?",', ' "', 'What', "'s", ' the', ' most', ' memorable', ' concert', ' or', ' live', ' show', ' you', "'ve", ' been', ' to', '?', '"]', ' }'],
      });
    };
    sinon
      .stub(replicateClient, 'getReplicateApiClient')
      .returns(fakeReplicateClient);
    res = await request(app)
      .put('/v1/ai/icebreakers')
      .set('authorization', 0)
      .send({
        user: '0',
        outputType: 'topical icebreakers', // 'topical icebreakers', 'pickup lines', 'jokes', or 'compliments'
        contextType: 'interests', // 'interests', 'bio', or 'unrelated'
        selectedInterests: ['interest'],
        previousResponses: ['output'],
      });

    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
    expect(res.body.output).to.be.an.instanceOf(Array);
    console.log('ice breaker', res.body.output);
    const expected = [
      "So, what's the most spontaneous thing you've ever done on a whim?",
      "What's the best book you've read recently? Why did it stand out to you?",
      "I saw you're into hiking! What's your favorite trail you've explored so far?",
      "What's the most memorable concert or live show you've been to?",
    ];
    expect(res.body.output.length).to.eql(expected.length);
    for (let i = 0; i < 4; i++) {
      expect(res.body.output[i]).to.eql(expected[i]);
    }
  });
});

describe('testing routes with replicate llama3', async () => {
  beforeEach(async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
        locale: 'en',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.aiSettings).to.eql({
      outputLanguage: 'en',
    });
    const fakeReplicateClient = {};

    fakeReplicateClient.createPrediction = (model, input) => {
      console.log('Fake replicate createPrediction', model, input);
      return Promise.resolve({
        id: 'xag3atv30xrgm0cf7yca88gpaw',
      });
    };
    fakeReplicateClient.getPrediction = (predictionId) => {
      console.log('Fake replicate getPrediction', predictionId);
      return Promise.resolve({
        metrics: {
          input_token_count: 10,
          output_token_count: 10,
        },
        output: ['Based', ' on', ' Công', "'s", ' profile', ',', ' here', ' are', ' four', ' concise', ' insights', ' into', ' his', ' likes', ',', ' dislikes', ',', ' and', ' how', ' to', ' best', ' connect', ' with', ' him', ':\n\n', '•', ' **', 'He', "'s", ' a', ' laid', '-back', ' and', ' flexible', ' guy', '**:', ' As', ' an', ' IST', 'P', ',', ' Công', ' is', ' likely', ' easy', '-going', ' and', ' adaptable', '.', ' He', ' might', ' not', ' be', ' too', ' f', 'ussed', ' about', ' planning', ' every', ' detail', ' of', ' a', ' date', ' or', ' conversation', ',', ' so', ' feel', ' free', ' to', ' suggest', ' something', ' spontaneous', ' or', ' go', ' with', ' the', ' flow', '.', ' This', ' also', ' means', ' he', "'s", ' probably', ' open', ' to', ' trying', ' new', ' things', ' and', ' exploring', ' different', ' topics', '.\n\n', '•', ' **', 'G', 'aming', ' and', ' entertainment', ' are', ' his', ' jam', '**:', ' With', ' interests', ' like', ' "', 'ch', 'ơ', 'ig', 'ame', '"', ' (', 'g', 'aming', ')', ' and', ' "', 'ph', 'im', '"', ' (', 'movies', '),', ' it', "'s", ' clear', ' Công', ' enjoys', ' having', ' fun', ' and', ' being', ' entertained', '.', ' Try', ' asking', ' him', ' about', ' his', ' favorite', ' games', ' or', ' movies', ',', ' or', ' even', ' suggest', ' playing', ' a', ' game', ' together', ' or', ' watching', ' a', ' film', '.', ' This', ' could', ' be', ' a', ' great', ' way', ' to', ' break', ' the', ' ice', ' and', ' find', ' common', ' ground', '.\n\n', '•', ' **', 'He', ' values', ' authenticity', ' and', ' sincerity', '**:', ' As', ' a', ' Cancer', ',', ' Công', ' is', ' likely', ' deeply', ' emotional', ' and', ' empath', 'etic', '.', ' When', ' reaching', ' out', ' to', ' him', ',', ' try', ' to', ' be', ' genuine', ' and', ' authentic', ' in', ' your', ' approach', '.', ' Avoid', ' coming', ' on', ' too', ' strong', ' or', ' trying', ' to', ' impress', ' him', ' with', ' superficial', ' things', ' –', ' instead', ',', ' focus', ' on', ' having', ' a', ' real', ' conversation', ' and', ' showing', ' interest', ' in', ' who', ' he', ' is', ' as', ' a', ' person', '.\n\n', '•', ' **', 'Res', 'pect', ' his', ' boundaries', ' and', ' don', "'t", ' be', ' too', ' push', 'y', '**:', ' IST', 'Ps', ' tend', ' to', ' value', ' their', ' independence', ' and', ' alone', ' time', ',', ' so', ' it', "'s", ' essential', ' to', ' respect', ' Công', "'s", ' boundaries', ' if', ' he', ' doesn', "'t", ' feel', ' like', ' talking', ' or', ' meeting', ' up', ' right', ' away', '.', ' Don', "'t", ' take', ' it', ' personally', ' if', ' he', ' needs', ' some', ' space', ' –', ' just', ' give', ' him', ' time', ' and', ' let', ' him', ' come', ' to', ' you', ' when', ' he', "'s", ' ready', '.', ''],
      });
    };
    sinon
      .stub(replicateClient, 'getReplicateApiClient')
      .returns(fakeReplicateClient);
  });

  it('testing route PUT /v1/ai/profileAnalysis', async () => {
    const res = await request(app)
      .put('/v1/ai/profileAnalysis')
      .set('authorization', 0)
      .send({
        user: '0',
        outputType: 'profile', // 'profile' or 'compatibility'
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
    let expected = ['Based', ' on', ' Công', "'s", ' profile', ',', ' here', ' are', ' four', ' concise', ' insights', ' into', ' his', ' likes', ',', ' dislikes', ',', ' and', ' how', ' to', ' best', ' connect', ' with', ' him', ':\n\n', '•', ' **', 'He', "'s", ' a', ' laid', '-back', ' and', ' flexible', ' guy', '**:', ' As', ' an', ' IST', 'P', ',', ' Công', ' is', ' likely', ' easy', '-going', ' and', ' adaptable', '.', ' He', ' might', ' not', ' be', ' too', ' f', 'ussed', ' about', ' planning', ' every', ' detail', ' of', ' a', ' date', ' or', ' conversation', ',', ' so', ' feel', ' free', ' to', ' suggest', ' something', ' spontaneous', ' or', ' go', ' with', ' the', ' flow', '.', ' This', ' also', ' means', ' he', "'s", ' probably', ' open', ' to', ' trying', ' new', ' things', ' and', ' exploring', ' different', ' topics', '.\n\n', '•', ' **', 'G', 'aming', ' and', ' entertainment', ' are', ' his', ' jam', '**:', ' With', ' interests', ' like', ' "', 'ch', 'ơ', 'ig', 'ame', '"', ' (', 'g', 'aming', ')', ' and', ' "', 'ph', 'im', '"', ' (', 'movies', '),', ' it', "'s", ' clear', ' Công', ' enjoys', ' having', ' fun', ' and', ' being', ' entertained', '.', ' Try', ' asking', ' him', ' about', ' his', ' favorite', ' games', ' or', ' movies', ',', ' or', ' even', ' suggest', ' playing', ' a', ' game', ' together', ' or', ' watching', ' a', ' film', '.', ' This', ' could', ' be', ' a', ' great', ' way', ' to', ' break', ' the', ' ice', ' and', ' find', ' common', ' ground', '.\n\n', '•', ' **', 'He', ' values', ' authenticity', ' and', ' sincerity', '**:', ' As', ' a', ' Cancer', ',', ' Công', ' is', ' likely', ' deeply', ' emotional', ' and', ' empath', 'etic', '.', ' When', ' reaching', ' out', ' to', ' him', ',', ' try', ' to', ' be', ' genuine', ' and', ' authentic', ' in', ' your', ' approach', '.', ' Avoid', ' coming', ' on', ' too', ' strong', ' or', ' trying', ' to', ' impress', ' him', ' with', ' superficial', ' things', ' –', ' instead', ',', ' focus', ' on', ' having', ' a', ' real', ' conversation', ' and', ' showing', ' interest', ' in', ' who', ' he', ' is', ' as', ' a', ' person', '.\n\n', '•', ' **', 'Res', 'pect', ' his', ' boundaries', ' and', ' don', "'t", ' be', ' too', ' push', 'y', '**:', ' IST', 'Ps', ' tend', ' to', ' value', ' their', ' independence', ' and', ' alone', ' time', ',', ' so', ' it', "'s", ' essential', ' to', ' respect', ' Công', "'s", ' boundaries', ' if', ' he', ' doesn', "'t", ' feel', ' like', ' talking', ' or', ' meeting', ' up', ' right', ' away', '.', ' Don', "'t", ' take', ' it', ' personally', ' if', ' he', ' needs', ' some', ' space', ' –', ' just', ' give', ' him', ' time', ' and', ' let', ' him', ' come', ' to', ' you', ' when', ' he', "'s", ' ready', '.', ''];
    expected = expected
      .join('')
      .replace(/(^|[^\n])\n(?!\n)/g, '$1\n\n')
      .replaceAll('**', '');
    expect(res.body.output).to.equal(expected);
  });

  it('testing route PUT /v1/ai/bio/generate', async () => {
    const res = await request(app)
      .put('/v1/ai/bio/generate')
      .set('authorization', 0)
      .send({
        context: {
          gender: true,
          age: true,
          mbti: true,
          location: true,
          languages: true,
          education: true,
          work: true,
          interests: true,
          lookingFor: true,
        },
        content: 'text',
        tone: 'text',
        previousResponses: ['output'],
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
    expect(res.body.output).to.be.an.instanceOf(Array);
  });

  it('creates a question and test route PUT /v1/ai/social with that question', async () => {
    const question = await createQuestion({
      createdAt: Date.now() + 90 * 24 * 60 * 60 * 1000, // 3 months
      text: 'question 3',
      interestName: 'questions',
    });

    const res = await request(app)
      .put('/v1/ai/social')
      .set('authorization', 0)
      .send({
        questionId: question._id,
        // commentId: 'id', // optional if responding to a comment
        outputType: 'suggest', // 'suggest' or 'paraphrase' or 'proofread'
        userInput: 'text',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
  });
});

describe('testing route PUT /v1/ai/chatAnalysis && /v1/ai/continueConversation with replicate llama3', async () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 2; uid++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({
          appVersion: '1.13.17',
          locale: 'en',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/education')
        .set('authorization', uid)
        .send({
          education: `education${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/description')
        .set('authorization', uid)
        .send({
          description: `description${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/work')
        .set('authorization', uid)
        .send({
          work: `work${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .get('/v1/user/quizQuestions')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      const answers = Object.keys(res.body.questions).reduce((map, id) => {
        map[id] = 0;
        return map;
      }, {});
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({ answers });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/handle')
        .set('authorization', uid)
        .send({
          handle: `handle${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          purpose: ['friends'],
        });
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: new Date().getFullYear() - 31,
          month: 1,
          day: 1,
        });
      // Honolulu, HI
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          latitude: 21.3,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/profilePromptAnswers')
        .set('authorization', uid)
        .send({
          prompts: [{ id: promptsLib.promptsArray[0].id, answer: `${uid}` }],
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/approveAllFollowers')
        .set('authorization', uid)
        .send({ approveAllFollowers: false });
      expect(res.status).to.equal(200);
      // mock upload two pictures
      const user = await User.findOne({ _id: uid });
      user.pictures.push('picture0');
      user.pictures.push('picture1');
      res = await user.save();
    }

    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    for (let i = 0; i < 8; i++) {
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 1)
        .send({
          user: '0',
          text: `hi ${i}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 0)
        .send({
          user: '1',
          text: `hello ${i}`,
        });
      expect(res.status).to.equal(200);
    }

    let fakeReplicateClient = {};
    fakeReplicateClient.createPrediction = (model, input) => {
      console.log('Fake replicate createPrediction', model, input);
      return Promise.resolve({
        id: 'xag3atv30xrgm0cf7yca88gpaw',
      });
    };
    fakeReplicateClient.getPrediction = (predictionId) => {
      console.log('Fake replicate getPrediction', predictionId);
      return Promise.resolve({
        metrics: {
          input_token_count: 10,
          output_token_count: 10,
        },
        output: ['{', ' output', ':', ' ["', 'So', ',', ' what', "'s", ' been', ' the', ' highlight', ' of', ' your', ' week', '?",', ' "', 'I', "'m", ' loving', ' the', ' sunshine', ' here', ' in', ' Honolulu', ',', ' how', ' about', ' you', '?",', ' "', 'I', "'m", ' curious', ',', ' do', ' you', ' have', ' any', ' fun', ' plans', ' for', ' the', ' weekend', '?",', ' "', 'It', "'s", ' nice', ' to', ' finally', ' have', ' someone', ' to', ' chat', ' with', ',', ' how', "'s", ' your', ' day', ' going', '?', '"]', ' }'],
      });
    };
    sinon
      .stub(replicateClient, 'getReplicateApiClient')
      .returns(fakeReplicateClient);
  });

  it('testing PUT /v1/ai/chatAnalysis', async () => {
    const res = await request(app)
      .put('/v1/ai/chatAnalysis')
      .set('authorization', 0)
      .send({
        user: '1',
        outputType: 'sentiment', // 'sentiment', 'performance', or 'intent'
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
  });

  it('testing route PUT /v1/ai/continueConversation', async () => {
    const res = await request(app)
      .put('/v1/ai/continueConversation')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.include.keys('output', 'numBooAINeurons');
  });
});
*/

describe('neurons', async () => {

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.17',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.numBooAINeurons).to.equal(3);
  });

  it('get neuron product ids', async () => {
    res = await request(app)
      .get('/v1/ai/neuronProductIds')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.neuronProductIds).to.eql([
      '1000_neurons',
      '300_neurons',
      '100_neurons',
      '40_neurons',
      '6_neurons',
    ]);
  });

  it('purchase neurons', async () => {
    let receipt = iapHelper.getValidAppleReceipt('6_neurons', Date.now());
    res = await request(app)
      .put('/v1/ai/purchaseNeurons')
      .set('authorization', 0)
      .send({
        receipt,
        price: 1000,
        currency: 'JPY',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.numBooAINeurons).to.equal(9);

    receipts = await NeuronPurchaseReceipt.find();
    expect(receipts.length).to.equal(1);
    expect(receipts[0].revenue).to.equal(10);
    expect(receipts[0].price).to.equal(1000);
    expect(receipts[0].currency).to.equal('JPY');
    expect(receipts[0].purchaseNumber).to.equal(1);

    user = await User.findById(0).lean();
    expect(user.purchases).to.eql([{
      productType: 'neurons',
      productId: '6_neurons',
      daysOnPlatformBeforePurchase: 0,
      revenue: 10,
    }]);

    user = await User.findById('0');
    expect(user.metrics.numNeuronsPurchased).to.equal(6);
    expect(user.metrics.numNeuronPurchases).to.equal(1);
    expect(user.metrics.neuronRevenue).to.equal(10);
    expect(user.metrics.revenue).to.equal(10);

    // second purchase
    receipt = iapHelper.getValidGoogleReceipt('6_neurons', Date.now());
    res = await request(app)
      .put('/v1/ai/purchaseNeurons')
      .set('authorization', 0)
      .send({
        receipt,
        price: 1000,
        currency: 'JPY',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.numBooAINeurons).to.equal(15);

    receipts = await NeuronPurchaseReceipt.find().sort('-createdAt');
    expect(receipts.length).to.equal(2);
    expect(receipts[0].revenue).to.equal(10);
    expect(receipts[0].price).to.equal(1000);
    expect(receipts[0].currency).to.equal('JPY');
    expect(receipts[0].purchaseNumber).to.equal(2);

    user = await User.findById('0');
    expect(user.metrics.numNeuronsPurchased).to.equal(12);
    expect(user.metrics.numNeuronPurchases).to.equal(2);
    expect(user.metrics.neuronRevenue).to.equal(20);
    expect(user.metrics.revenue).to.equal(20);

    // check purchase receipt
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    res = await request(app)
      .get('/v1/admin/user/neuronPurchases')
      .set('authorization', 0)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.purchases.length).to.equal(2);
    expect(res.body.purchases[0].productId).to.equal('6_neurons');
    expect(res.body.purchases[1].productId).to.equal('6_neurons');
  });

  it('social proof', async () => {
    receipt = iapHelper.getValidAppleReceipt('6_neurons', Date.now());
    res = await request(app)
      .put('/v1/ai/purchaseNeurons')
      .set('authorization', 0)
      .send({
        receipt,
        price: 1000,
        currency: 'JPY',
      });
    expect(res.status).to.equal(200);

    receipt = iapHelper.getValidAppleReceipt('100_neurons', Date.now());
    res = await request(app)
      .put('/v1/ai/purchaseNeurons')
      .set('authorization', 0)
      .send({
        receipt,
        price: 1000,
        currency: 'JPY',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/social-proof/neuron-purchase')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.images.length).to.equal(0);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    const pictureId = res.body.pictures[0];

    res = await request(app)
      .get('/v1/social-proof/neuron-purchase')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.images.length).to.equal(1);
    expect(res.body.images[0]).to.equal(pictureId);
  });
});

it('ai settings', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({
      appVersion: '1.13.17',
      locale: 'de',
    });
  expect(res.status).to.equal(200);
  expect(res.body.user.aiSettings).to.eql({
    outputLanguage: 'de',
  });

  res = await request(app)
    .get('/v1/ai/prices')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.prices).to.eql({
    icebreakers: 2,
    continueConversation: 4,
    analysis: 4,
    suggest: 3,
    paraphrase: 2,
    proofread: 1,
    bioGenerate: 3,
    bioImprove: 4,
    bioTone: 4,
    bioProofread: 2,
  });

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({
      appVersion: '1.13.17',
      locale: 'en',
    });
  expect(res.status).to.equal(200);
  expect(res.body.user.aiSettings).to.eql({
    outputLanguage: 'en',
  });

  res = await request(app)
    .get('/v1/ai/prices')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.prices).to.eql({
    icebreakers: 1,
    continueConversation: 3,
    analysis: 3,
    suggest: 2,
    paraphrase: 2,
    proofread: 1,
    bioGenerate: 2,
    bioImprove: 2,
    bioTone: 3,
    bioProofread: 2,
  });

  res = await request(app)
    .put('/v1/ai/settings')
    .set('authorization', 0)
    .send({
      aiSettings: {
        outputLanguage: 'es',
        tone: 'clown',
      },
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.aiSettings).to.eql({
    outputLanguage: 'es',
    tone: 'clown',
  });

  res = await request(app)
    .get('/v1/ai/prices')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.prices).to.eql({
    icebreakers: 2,
    continueConversation: 3,
    analysis: 3,
    suggest: 2,
    paraphrase: 2,
    proofread: 1,
    bioGenerate: 3,
    bioImprove: 4,
    bioTone: 3,
    bioProofread: 2,
  });
});

it('not enough chat history', async () => {
  // create matched users without chat history
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({
      appVersion: '1.13.17',
      locale: 'en',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({
      appVersion: '1.13.17',
      locale: 'en',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/ai/chatAnalysis')
    .set('authorization', 0)
    .send({
      user: '1',
      outputType: 'sentiment',
    });
  expect(res.status).to.equal(499);
  expect(res.text).to.equal('Not enough chat history');

  res = await request(app)
    .put('/v1/ai/continueConversation')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(499);
  expect(res.text).to.equal('Not enough chat history');
});

describe('get users profile analysis', () => {
  let openAiStub;
  beforeEach(() => {
    openAiStub = sinon.stub(OpenAI.prototype, 'executePrompt').resolves({
      output: JSON.stringify({
        output: {
          insights_overall: {
            overall: {
              feedback: ' 1-2 sentence feedback summary?'
            },
            items: []
          },
          insights_biography: {
            overall: {
              score: 10,
              feedback: 'bio evaluation string.',
              insights: 'bio insights string. '
            },
            items: []
          },
          insights_photos: {
            overall: {
              feedback: 'overall photo feedback! ',
              score: 7
            },
            items: [
              {
                key: 'Photo 1',
                photoUrl: 'MOCK_IMAGE_DOMAIN/1748657408952bd812a8c1/91e131b099eb938.jpg',
                evaluation: 'Good photo.',
                insights: 'insights for photo 1!'
              },
              {
                key: 'Photo 2',
                photoUrl: 'MOCK_IMAGE_DOMAIN/1748657408952bd812a8c1/91e131b099eb9381.jpg',
                evaluation: 'Background issue.',
                insights: 'insights for photo 2?'
              },
            ]
          },
          insights_prompts: {
            overall: {
              feedback: 'overall prompt feedback?',
              score: 8
            },
            items: [
              {
                key: 'Prompt 1',
                evaluation: 'Good',
                insights: 'insights for prompt 1.'
              },
              {
                key: 'Prompt 2',
                evaluation: 'Needs Improvement',
                insights: 'insights for prompt 2,'
              },
              {
                key: 'Prompt 3',
                evaluation: 'evaluation for prompt 3,',
                insights: ' insights for prompt 3.'
              }
            ]
          }
        },
      }),
      cost: 0.002,
      promptTokens: 50,
      outputTokens: 10,
      errorMessage: null,
      processingTime: 1089,
    });
  });
  afterEach(() => {
    openAiStub.restore();
  });
  it('app_353 experiment for profile analysis for user', async () => {
    const promptResponse =  {
            verificationStatus: 'unverified',
            insights_overall: {
              overall: {
                feedback: '1-2 sentence feedback summary?'
              }
            },
            insights_biography: {
              overall: {
                score: 10,
                feedback: 'bio evaluation string',
                insights: 'bio insights string.'
              }
            },
            insights_photos: {
              overall: {
                feedback: 'overall photo feedback!',
                score: 7
              },
              items: [
                {
                  key: 'Photo 1',
                  photoUrl: 'MOCK_IMAGE_DOMAIN/1748657408952bd812a8c1/91e131b099eb938.jpg',
                  evaluation: 'Bonne Photo',
                  insights: 'insights for photo 1!'
                },
                {
                  key: 'Photo 2',
                  photoUrl: 'MOCK_IMAGE_DOMAIN/1748657408952bd812a8c1/91e131b099eb9381.jpg',
                  evaluation: `Problème d'Arrière-Plan`,
                  insights: 'insights for photo 2?'
                },
              ]
            },
            insights_prompts: {
              overall: {
                feedback: 'overall prompt feedback?',
                score: 8
              },
              items: [
                {
                  key: 'Prompt 1',
                  evaluation: 'Bien',
                  insights: 'insights for prompt 1.'
                },
                {
                  key: 'Prompt 2',
                  evaluation: 'À Améliorer',
                  insights: 'insights for prompt 2,'
                },
              ]
            }
          }
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.86' });
    expect(res.status).to.equal(200);

    //change locale of user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.86', locale: 'fr' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 0)
      .send({
        mbti: 'ESTJ',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .put('/v1/user/moreAboutUser/religion')
      .set('authorization', 0)
      .send({
        religion: 'Other',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/profilePromptAnswers')
      .set('authorization', 0)
      .send({
        prompts: [
          { id: promptsLib.promptsArray[0].id, answer: '0' },
          { id: promptsLib.promptsArray[1].id, answer: '0' },
        ],
      });
    expect(res.status).to.equal(200);

    // added this to avoid errors for image as the openAI response is stubbed
    let user = await User.findOne({ _id: 0 });
    user.pictures = ['1748657408952bd812a8c1/91e131b099eb938.jpg', '1748657408952bd812a8c1/91e131b099eb9381.jpg']
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.85' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.84' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/ai/profileAnalysis')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.analyzedResults).to.eql(promptResponse)

    let savedAnalysis = await UsersProfileAnalysisPrompt.find({ userId: '0' })
    expect(savedAnalysis.length).to.equal(1);
    expect(savedAnalysis[0].userId).to.equal('0');
    expect(typeof savedAnalysis[0].prompt).to.equal('string');

    res = await request(app)
      .get('/v1/ai/profileAnalysis')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.analyzedResults).to.eql(promptResponse)

    //returned same data
    savedAnalysis = await UsersProfileAnalysisPrompt.find({ userId: '0' })
    expect(savedAnalysis.length).to.equal(1);
    expect(savedAnalysis[0].userId).to.equal('0');
    expect(typeof savedAnalysis[0].prompt).to.equal('string');

    // update description
    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 0)
      .send({
        description: 'new description',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/ai/profileAnalysis')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.analyzedResults).to.eql(promptResponse)

    savedAnalysis = await UsersProfileAnalysisPrompt.find({ userId: '0' })
    expect(savedAnalysis.length).to.equal(2);
    expect(savedAnalysis[0].userId).to.equal('0');
    expect(typeof savedAnalysis[0].prompt).to.equal('string');
    expect(savedAnalysis[1].userId).to.equal('0');
    expect(typeof savedAnalysis[1].prompt).to.equal('string');

    user = await User.findOne({ _id: 0 });
    user.verification = { status: 'verified' }
    await user.save();

    res = await request(app)
      .get('/v1/ai/profileAnalysis')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.analyzedResults.verificationStatus).to.equal('verified');

    user = await User.findOne({ _id: 0 });
    user.verification = { status: 'pending' }
    await user.save();

    res = await request(app)
      .get('/v1/ai/profileAnalysis')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.analyzedResults.verificationStatus).to.equal('unverified');

    savedAnalysis = await UsersProfileAnalysisPrompt.find({ userId: '0' })
    expect(savedAnalysis.length).to.equal(2);
    expect(savedAnalysis[0].userId).to.equal('0');
    expect(typeof savedAnalysis[0].prompt).to.equal('string');
    expect(savedAnalysis[1].userId).to.equal('0');
    expect(typeof savedAnalysis[1].prompt).to.equal('string');

    user = await User.findOne({ _id: 0 });
    expect(user.fetchNewProfileAnalysis).to.equal(false);

    //update prompts
    res = await request(app)
      .put('/v1/user/profilePromptAnswers')
      .set('authorization', 0)
      .send({
        prompts: [
          { id: promptsLib.promptsArray[0].id, answer: '0' },
          { id: promptsLib.promptsArray[1].id, answer: '0' },
        ],
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    expect(user.fetchNewProfileAnalysis).to.equal(true);

    res = await request(app)
      .get('/v1/ai/profileAnalysis')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.analyzedResults).to.eql(promptResponse)

    user = await User.findOne({ _id: 0 });
    expect(user.fetchNewProfileAnalysis).to.equal(false);

    savedAnalysis = await UsersProfileAnalysisPrompt.find({ userId: '0' })
    expect(savedAnalysis.length).to.equal(3);
    expect(savedAnalysis[0].userId).to.equal('0');
    expect(typeof savedAnalysis[0].prompt).to.equal('string');
    expect(savedAnalysis[1].userId).to.equal('0');
    expect(typeof savedAnalysis[1].prompt).to.equal('string');
    expect(savedAnalysis[2].userId).to.equal('0');
    expect(typeof savedAnalysis[2].prompt).to.equal('string');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(2);
    let pictures = res.body.user.pictures;

    user = await User.findOne({ _id: 0 });
    expect(user.fetchNewProfileAnalysis).to.equal(false);

    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 0)
      .query({ id: pictures[1] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    expect(user.pictures.length).to.equal(2);
    expect(user.originalPictures.length).to.equal(2);
    expect(user.fetchNewProfileAnalysis).to.equal(true);

    res = await request(app)
      .get('/v1/ai/profileAnalysis')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.analyzedResults).to.eql(promptResponse)

    user = await User.findOne({ _id: 0 });
    expect(user.fetchNewProfileAnalysis).to.equal(false);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures.length).to.equal(2);
    pictures = res.body.user.pictures;

    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 0)
      .query({ id: pictures[0] });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    expect(user.fetchNewProfileAnalysis).to.equal(true);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    expect(user.fetchNewProfileAnalysis).to.equal(true);
    expect(user.originalPictures.length).to.equal(2);

    let originalPictures = [user.originalPictures[1], user.originalPictures[0]];

    res = await request(app)
      .put('/v1/user/reorderPictures')
      .set('authorization', 0)
      .send({ ids: originalPictures })
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    expect(user.fetchNewProfileAnalysis).to.equal(true);
    expect(user.originalPictures).to.eql(originalPictures);
    expect(user.pictures).to.eql(originalPictures);

    user = await User.findOne({ _id: 0 });
    user.fetchNewProfileAnalysis = false
    await user.save()

    originalPictures = [user.originalPictures[1], user.originalPictures[0]];

    res = await request(app)
      .put('/v1/user/reorderPictures')
      .set('authorization', 0)
      .send({ ids: originalPictures })
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    expect(user.fetchNewProfileAnalysis).to.equal(false);
    expect(user.originalPictures).to.eql(originalPictures);
    expect(user.pictures).to.eql(originalPictures);

  })

  it('should handle malformed OpenAI output where insights are nested under insights_overall', async () => {
    openAiStub.restore();
    openAiStub = sinon.stub(OpenAI.prototype, 'executePrompt').resolves({
      output: JSON.stringify({
        output: {
          insights_overall: {
            overall: {
              feedback: 'Overall feedback summary'
            },
            insights_biography: {
              overall: {
                score: 9,
                feedback: 'Bio evaluation from malformed response',
                insights: 'Bio insights from malformed response'
              }
            },
            insights_photos: {
              overall: {
                feedback: 'Photo feedback from malformed response',
                score: 8
              },
              items: [
                {
                  key: 'Photo 1',
                  photoUrl: 'MOCK_IMAGE_DOMAIN/malformed/test.jpg',
                  evaluation: 'Malformed photo evaluation',
                  insights: 'Malformed photo insights'
                }
              ]
            },
            insights_prompts: {
              overall: {
                feedback: 'Prompt feedback from malformed response',
                score: 7
              },
              items: [
                {
                  key: 'Prompt 1',
                  evaluation: 'Malformed prompt evaluation',
                  insights: 'Malformed prompt insights'
                }
              ]
            }
          }
        }
      }),
      cost: 0.003,
      promptTokens: 60,
      outputTokens: 15,
      errorMessage: null,
      processingTime: 1200,
    });

    const expectedResponse = {
      verificationStatus: 'unverified',
      insights_overall: {
        overall: {
          feedback: 'Overall feedback summary'
        }
      },
      insights_biography: {
        overall: {
          score: 9,
          feedback: 'Bio evaluation from malformed response',
          insights: 'Bio insights from malformed response'
        }
      },
      insights_photos: {
        overall: {
          feedback: 'Photo feedback from malformed response',
          score: 8
        },
        items: [
          {
            key: 'Photo 1',
            photoUrl: 'MOCK_IMAGE_DOMAIN/malformed/test.jpg',
            evaluation: 'Malformed photo evaluation',
            insights: 'Malformed photo insights'
          }
        ]
      },
      insights_prompts: {
        overall: {
          feedback: 'Prompt feedback from malformed response',
          score: 7
        },
        items: [
          {
            key: 'Prompt 1',
            evaluation: 'Malformed prompt evaluation',
            insights: 'Malformed prompt insights'
          }
        ]
      }
    };

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/profilePromptAnswers')
      .set('authorization', 0)
      .send({
        prompts: [
          { id: promptsLib.promptsArray[0].id, answer: '0' },
        ],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/ai/profileAnalysis')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.analyzedResults).to.eql(expectedResponse);
    expect(res.body.analyzedResults.insights_overall).to.exist;
    expect(res.body.analyzedResults.insights_biography).to.exist;
    expect(res.body.analyzedResults.insights_photos).to.exist;
    expect(res.body.analyzedResults.insights_prompts).to.exist;
    expect(res.body.analyzedResults.insights_overall.insights_biography).to.not.exist;
    expect(res.body.analyzedResults.insights_overall.insights_photos).to.not.exist;
    expect(res.body.analyzedResults.insights_overall.insights_prompts).to.not.exist;
  });
})

describe('getChatAnalysisResultForYourTurn JSON parsing error handling', () => {
  let openAiStub;

  beforeEach(() => {
    openAiStub = sinon.stub(OpenAI.prototype, 'executePrompt');
  });

  afterEach(() => {
    openAiStub.restore();
  });

  it('should handle malformed JSON with markdown formatting and newlines', async () => {
    const malformedJsonResponse = {
      output: '```json\n{\n  "output": {\n    "Status": "Active",\n    "Contact details": "no",\n    "Met up": "no",\n    "Transactions": "no",\n    "Explanation": "The conversation appears active"\n  }\n}\n```',
      cost: 0.001,
      promptTokens: 100,
      outputTokens: 50
    };

    openAiStub.resolves(malformedJsonResponse);

    const recentChat = {
      messages: 'Hello there! How are you?',
      imageUrls: []
    };

    const result = await getChatAnalysisResultForYourTurn(recentChat);

    expect(result).to.have.property('formattedOutputPrompts');
    expect(result.formattedOutputPrompts).to.have.property('Status', 'Active');
    expect(result.formattedOutputPrompts).to.have.property('Contact details', 'no');
    expect(result.formattedOutputPrompts).to.have.property('Met up', 'no');
    expect(result.formattedOutputPrompts).to.have.property('Transactions', 'no');
    expect(result.formattedOutputPrompts).to.have.property('Explanation', 'The conversation appears active');
  });

  it('should handle truly malformed JSON with proper error handling', async () => {
    const malformedJsonResponse = {
      output: '```json\n{\n  "output": {\n    "Status": "Active",\n    "Contact details": "no",\n    "Met up": "no",\n    "Transactions": "no",\n    "Explanation": "The conversation appears active"\n  }\n  // Missing closing brace\n```',
      cost: 0.001,
      promptTokens: 100,
      outputTokens: 50
    };

    openAiStub.resolves(malformedJsonResponse);

    const recentChat = {
      messages: 'Hello there! How are you?',
      imageUrls: []
    };

    const result = await getChatAnalysisResultForYourTurn(recentChat);

    expect(result).to.have.property('errorMessage');
    expect(result.errorMessage).to.include('JSON parsing error');
    expect(result).to.have.property('formattedOutputPrompts');
    expect(result.formattedOutputPrompts).to.be.empty;
  });

});
