const { expect } = require('chai');
const request = require('supertest');
const app = require('../lib/server');
const Profile = require('../models/profile');
const Subcategory = require('../models/subcategory');
const SubcategoryPaginatedProfiles = require('../models/subcategory-paginated-profiles');
const {
  getAllSubcategoryIds,
  getPaginatedProfileIds,
  createPaginatedProfilesForSubcategory,
  updateSubcategoryTotalPages,
  cleanupExcessPages,
  getTotalPagesForSubcategory,
  processSubcategory,
  backfillSubcategoryPagination
} = require('../lib/subcategory-pagination');

describe('Subcategory Pagination', () => {
  beforeEach(async () => {
    // Clean up test data
    await Profile.deleteMany({});
    await Subcategory.deleteMany({});
    await SubcategoryPaginatedProfiles.deleteMany({});
  });

  describe('Library Functions', () => {
    beforeEach(async () => {
      // Create test subcategories
      await Subcategory.create([
        { id: 1, name: 'Test Category 1', slug: 'test-1', category: 1, numProfiles: 0 },
        { id: 2, name: 'Test Category 2', slug: 'test-2', category: 1, numProfiles: 0 }
      ]);

      // Create test profiles
      await Profile.create([
        { id: 1, name: 'Profile 1', slug: 'profile-1', subcategories: [1], sort: 100 },
        { id: 2, name: 'Profile 2', slug: 'profile-2', subcategories: [1], sort: 200 },
        { id: 3, name: 'Profile 3', slug: 'profile-3', subcategories: [1], sort: 150 },
        { id: 4, name: 'Profile 4', slug: 'profile-4', subcategories: [2], sort: 300 },
        { id: 5, name: 'Profile 5', slug: 'profile-5', subcategories: [1, 2], sort: 250 }
      ]);
    });

    it('should get all subcategory IDs', async () => {
      const subcategoryIds = await getAllSubcategoryIds();
      expect(subcategoryIds).to.be.an('array');
      expect(subcategoryIds).to.have.lengthOf(2);
      expect(subcategoryIds).to.include.members([1, 2]);
    });

    it('should create paginated profiles for a subcategory', async () => {
      const totalPages = await createPaginatedProfilesForSubcategory(1);
      
      // With page size 2 (test environment), 4 profiles should create 2 pages
      expect(totalPages).to.equal(2);
      
      // Check first page
      const page1Profiles = await getPaginatedProfileIds(1, 1);
      expect(page1Profiles).to.have.lengthOf(2);
      expect(page1Profiles).to.deep.equal([1, 3]); // sorted by sort: 1, id: 1
      
      // Check second page
      const page2Profiles = await getPaginatedProfileIds(1, 2);
      expect(page2Profiles).to.have.lengthOf(2);
      expect(page2Profiles).to.deep.equal([2, 5]); // sorted by sort: 1, id: 1
    });

    it('should return null for non-existent page', async () => {
      await createPaginatedProfilesForSubcategory(1);
      const nonExistentPage = await getPaginatedProfileIds(1, 999);
      expect(nonExistentPage).to.be.null;
    });

    it('should update subcategory total pages', async () => {
      await updateSubcategoryTotalPages(1, 5);
      const subcategory = await Subcategory.findOne({ id: 1 });
      expect(subcategory.totalProfilePages).to.equal(5);
    });

    it('should cleanup excess pages', async () => {
      // Create some paginated data
      await SubcategoryPaginatedProfiles.create([
        { subcategoryId: 1, pageNo: 1, profileIds: [1, 2] },
        { subcategoryId: 1, pageNo: 2, profileIds: [3, 4] },
        { subcategoryId: 1, pageNo: 3, profileIds: [5] },
        { subcategoryId: 1, pageNo: 4, profileIds: [6] }
      ]);

      await cleanupExcessPages(1, 2);
      
      const remainingPages = await SubcategoryPaginatedProfiles.find({ subcategoryId: 1 });
      expect(remainingPages).to.have.lengthOf(2);
      expect(remainingPages.map(p => p.pageNo)).to.deep.equal([1, 2]);
    });

    it('should get total pages for subcategory', async () => {
      await SubcategoryPaginatedProfiles.create([
        { subcategoryId: 1, pageNo: 1, profileIds: [1, 2] },
        { subcategoryId: 1, pageNo: 2, profileIds: [3, 4] },
        { subcategoryId: 1, pageNo: 3, profileIds: [5] }
      ]);

      const totalPages = await getTotalPagesForSubcategory(1);
      expect(totalPages).to.equal(3);
    });

    it('should process a single subcategory completely', async () => {
      const result = await processSubcategory(1);
      
      expect(result).to.have.property('subcategoryId', 1);
      expect(result).to.have.property('totalPages', 2);
      expect(result).to.have.property('profileCount', 4);
      
      // Verify subcategory was updated
      const subcategory = await Subcategory.findOne({ id: 1 });
      expect(subcategory.totalProfilePages).to.equal(2);
      
      // Verify paginated data was created
      const paginatedData = await SubcategoryPaginatedProfiles.find({ subcategoryId: 1 });
      expect(paginatedData).to.have.lengthOf(2);
    });
  });

  describe('Backfill Function', () => {
    beforeEach(async () => {
      // Create test data for backfill
      await Subcategory.create([
        { id: 1, name: 'Test Category 1', slug: 'test-1', category: 1, numProfiles: 2 },
        { id: 2, name: 'Test Category 2', slug: 'test-2', category: 1, numProfiles: 1 }
      ]);

      await Profile.create([
        { id: 1, name: 'Profile 1', slug: 'profile-1', subcategories: [1], sort: 100 },
        { id: 2, name: 'Profile 2', slug: 'profile-2', subcategories: [1], sort: 200 },
        { id: 3, name: 'Profile 3', slug: 'profile-3', subcategories: [2], sort: 150 }
      ]);
    });

    it('should backfill all subcategories', async () => {
      const result = await backfillSubcategoryPagination({ logProgress: false });
      
      expect(result.totalSubcategories).to.equal(2);
      expect(result.processedSubcategories).to.equal(2);
      expect(result.totalPages).to.equal(2); // 1 page for subcategory 1, 1 page for subcategory 2
      expect(result.totalProfiles).to.equal(3);
      expect(result.errors).to.have.lengthOf(0);
      
      // Verify all subcategories were processed
      const subcategories = await Subcategory.find({});
      subcategories.forEach(sub => {
        expect(sub.totalProfilePages).to.be.a('number');
        expect(sub.totalProfilePages).to.be.greaterThan(0);
      });
    });
  });

  describe('Route Integration', () => {
    beforeEach(async () => {
      // Create test data
      await Subcategory.create([
        { id: 3, name: 'Test Category', slug: 'test-category', category: 1, numProfiles: 4, totalProfilePages: 2 }
      ]);

      await Profile.create([
        { id: 1, name: 'Profile 1', slug: 'profile-1', subcategories: [3], sort: 100 },
        { id: 2, name: 'Profile 2', slug: 'profile-2', subcategories: [3], sort: 200 },
        { id: 3, name: 'Profile 3', slug: 'profile-3', subcategories: [3], sort: 150 },
        { id: 4, name: 'Profile 4', slug: 'profile-4', subcategories: [3], sort: 250 }
      ]);

      // Create paginated data
      await SubcategoryPaginatedProfiles.create([
        { subcategoryId: 3, pageNo: 1, profileIds: [1, 3] },
        { subcategoryId: 3, pageNo: 2, profileIds: [2, 4] }
      ]);
    });

    it('should use paginated collection when page size matches', async () => {
      const res = await request(app)
        .get('/web/cached/profiles/subcategory/sitemap')
        .query({ subCategoryId: 3, pageSize: 2, page: 1 });
      
      expect(res.status).to.equal(200);
      expect(res.body.totalPages).to.equal(2);
      expect(res.body.profiles).to.have.lengthOf(2);
      expect(res.body.profiles[0].id).to.equal(1);
      expect(res.body.profiles[1].id).to.equal(3);
    });

    it('should get second page from paginated collection', async () => {
      const res = await request(app)
        .get('/web/cached/profiles/subcategory/sitemap')
        .query({ subCategoryId: 3, pageSize: 2, page: 2 });
      
      expect(res.status).to.equal(200);
      expect(res.body.totalPages).to.equal(2);
      expect(res.body.profiles).to.have.lengthOf(2);
      expect(res.body.profiles[0].id).to.equal(2);
      expect(res.body.profiles[1].id).to.equal(4);
    });

    it('should fall back to original method when page size does not match', async () => {
      const res = await request(app)
        .get('/web/cached/profiles/subcategory/sitemap')
        .query({ subCategoryId: 3, pageSize: 100 });
      
      expect(res.status).to.equal(200);
      expect(res.body.profiles).to.have.lengthOf(4);
    });

    it('should fall back to original method when lastProfileId is provided', async () => {
      const res = await request(app)
        .get('/web/cached/profiles/subcategory/sitemap')
        .query({ subCategoryId: 3, pageSize: 2, lastProfileId: 2 });
      
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.be.greaterThan(0);
    });

    it('should handle non-existent subcategory', async () => {
      const res = await request(app)
        .get('/web/cached/profiles/subcategory/sitemap')
        .query({ subCategoryId: 999, pageSize: 2 });
      
      expect(res.status).to.equal(400);
    });

    it('should handle empty page', async () => {
      const res = await request(app)
        .get('/web/cached/profiles/subcategory/sitemap')
        .query({ subCategoryId: 3, pageSize: 2, page: 999 });
      
      expect(res.status).to.equal(200);
      expect(res.body.profiles).to.have.lengthOf(0);
    });
  });
});
