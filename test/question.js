const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const { app, waitMs, validImagePath, validVideoPath } = require('./common');
const { REPORTS_UNTIL_DELETION } = require('../lib/constants');
const { createQuestion, backFillUserAttributes } = require('../lib/social');
const Question = require('../models/question');
const QuestionViewData = require('../models/question-view-data');
const User = require('../models/user');
const UserMetadata = require('../models/user-metadata');
const LanguageMismatch = require('../models/language-mismatch');
const {
  initApp, postQuestion, fetchCoinData, getQuestion, getQuestionFeed, getAllQuestions, setUserInterests, likeQuestion, awardQuestion, boostQuestion,
  setEnneagram, setUserPreferences, setPersonality, setUserBirthday, setLocation,
  sendUserLike, approveUser, setFcmToken, getIndividualChat, postQuestionImage, deleteQuestionImage,deleteQuestionImages
} = require('./helper/api');
const coinsConstants = require('../lib/coins-constants');
const { notifs, reset, waitFor, setMockPromptResponse, setMockImageModerationResponse, setS3MockContentList, fakeAdminMessaging } = require('./stub');
const googleTranslate = require('../lib/google-translate');
const sinon = require('sinon');
const metricsLib = require('../lib/metrics');
const openai = require('../lib/openai');
const PostReport = require('../models/post-report');
const ImageModeration = require('../models/image-moderation');
const s3 = require('../lib/s3');
const PostModeration = require('../models/post-moderation');
const { IMAGE_DOMAIN } = require('../lib/constants');

describe('Post notification test', () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 5; uid++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);

      if (uid % 2 === 0) {
        const user = await User.findById(uid);
        user.config.hive_image_moderation = true;
        await user.save();
      }
    }
  });

  it('should test notification for text based post', async () => {
    const sendStub = sinon.stub(fakeAdminMessaging, 'send').callsFake((params) => new Promise((resolve, reject) => {
      console.log('Fake messaging().send() ', params);
      if (params.token === 'invalidToken') {
        return reject(new Error('Fake error'));
      }
      resolve({ response: 'success' });
    }));

    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mentionedUsersTitle: [
          {
            _id: '1',
            firstName: 'name 1',
          },
        ],
      });
    expect(res.status).to.equal(200);

    // This post should be banned, so no notification should be sent
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'second scammer post',
        mentionedUsersTitle: [
          {
            _id: '1',
            firstName: 'name 1',
          },
        ],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    // Notification should be sent for first post only
    sinon.assert.calledOnce(sendStub);
  });

  it('should test notification for image based post', async () => {
    const sendStub = sinon.stub(fakeAdminMessaging, 'send').callsFake((params) => new Promise((resolve, reject) => {
      console.log('Fake messaging().send() ', params);
      if (params.token === 'invalidToken') {
        return reject(new Error('Fake error'));
      }
      resolve({ response: 'success' });
    }));

    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
        mentionedUsersTitle: [
          {
            _id: '1',
            firstName: 'name 1',
          },
        ],
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // finish uploading image
    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // Setup moderation response
    const rekognitionResponse = {
      isFlagged: false,
      detectionLabels: [],
    };

    const hiveResponse = {
      isFlagged: true,
      detectionLabels: [
        {
          ParentName: '',
          Name: 'general_nsfw',
          Confidence: 99.60970282554626,
        },
      ],
      flaggedModerationLabel: {
        ParentName: '',
        Name: 'general_nsfw',
        Confidence: 99.60970282554626,
      },
    };

    setMockImageModerationResponse(rekognitionResponse, hiveResponse);

    // Post will be banned by hive, so no notification should be sent
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
        mentionedUsersTitle: [
          {
            _id: '1',
            firstName: 'name 1',
          },
          {
            _id: '2',
            firstName: 'name 1',
          },
          {
            _id: '3',
            firstName: 'name 1',
          },
          {
            _id: '4',
            firstName: 'name 1',
          },
        ],
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q2Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // upload image for first post again, notification should not be sent again
    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    sinon.assert.calledOnce(sendStub);
  });
});

describe('image moderation test', () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 3; uid++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
    }
  });

  it('should display or hide image post based on moderation config', async () => {
    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 2)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // Setup moderation response
    const rekognitionResponse = {
      isFlagged: false,
      detectionLabels: [],
    };

    const hiveResponse = {
      isFlagged: true,
      detectionLabels: [
        {
          ParentName: '',
          Name: 'general_nsfw',
          Confidence: 99.60970282554626,
        },
      ],
      flaggedModerationLabel: {
        ParentName: '',
        Name: 'general_nsfw',
        Confidence: 99.60970282554626,
      },
    };

    setMockImageModerationResponse(rekognitionResponse, hiveResponse);

    // finish uploading image
    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 2)
      .query({ questionId: q1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    const question = await Question.findById(q1Id);
    const bannedReason = JSON.parse(question.bannedReason);
    expect(bannedReason.hive_v2).to.equal('general_nsfw');

    const modResults = await ImageModeration.find({});
    expect(modResults.length).to.equal(1);
    expect(modResults[0].isFlagged).to.equal(true);
    expect(modResults[0].serviceName).to.equal('Hive');

    // Post is visible to creator
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // Hive flagged the post, so will not be visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('should display or hide video post based on moderation config', async () => {
    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 2)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // Setup moderation response
    const rekognitionResponse = {
      isFlagged: false,
      detectionLabels: [],
    };

    const hiveResponse = {
      isFlagged: true,
      detectionLabels: [
        {
          ParentName: '',
          Name: 'general_nsfw',
          Confidence: 99.60970282554626,
        },
      ],
      flaggedModerationLabel: {
        ParentName: '',
        Name: 'general_nsfw',
        Confidence: 99.60970282554626,
      },
    };

    setMockImageModerationResponse(rekognitionResponse, hiveResponse);
    setS3MockContentList({
      Contents: [{
        Key: "photos/photo1.jpg",
      },
      {
        Key: "photos/photo2.jpg",
      }],
    });

    // finish uploading video
    res = await request(app)
      .post('/v1/question/video')
      .set('authorization', 2)
      .query({ questionId: q1Id })
      .attach('video', validVideoPath);
    expect(res.status).to.equal(200);

    // Post is visible to creator
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // Flagged by Hive, post will not be visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('should test posts ban with existing banned reasons', async () => {
    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 2)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // Setup moderation response
    const rekognitionResponse = {
      isFlagged: false,
      detectionLabels: [],
    };

    const hiveResponse = {
      isFlagged: true,
      detectionLabels: [
        {
          ParentName: '',
          Name: 'general_nsfw',
          Confidence: 99.60970282554626,
        },
      ],
      flaggedModerationLabel: {
        ParentName: '',
        Name: 'general_nsfw',
        Confidence: 99.60970282554626,
      },
    };

    setMockImageModerationResponse(rekognitionResponse, hiveResponse);
    setS3MockContentList({
      Contents: [{
        Key: "photos/photo1.jpg",
      },
      {
        Key: "photos/photo2.jpg",
      }],
    });

    // finish uploading video
    res = await request(app)
      .post('/v1/question/video')
      .set('authorization', 2)
      .query({ questionId: q1Id })
      .attach('video', validVideoPath);
    expect(res.status).to.equal(200);

    // Updating banned reason with previous structured banned reason
    const question = await Question.findById(q1Id);
    question.bannedReason = 'Exposed Male Genitalia';
    await question.save();

    // Should be visible to creator
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // This post was moderated previously, so it should be hidden to both users
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // Few posts has banned true with no banned reasons
    question.bannedReason = undefined;
    await question.save();

    // Post has no banned reason, it should be hidden to both users
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // Few posts has banned true and banned reason has json with different structure
    question.bannedReason = "{\"Confidence\":93.597900390625,\"Name\":\"Explicit Nudity\",\"ParentName\":\"Explicit\"}";
    await question.save();

    // Post has banned reason and json but different structure, it should be hidden to both users
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });
});

describe('testing user post report with same device ID', () => {
  beforeEach(async () => {
    for (let i = 0; i < 4; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      if (i % 2 === 0) {
        const user = await User.findById(i);
        user.deviceId = 'same-device-id';
        await user.save();
      }
    }
  });

  it('should not create report if post was reported with same device ID user', async () => {
    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'chess',
        title: 'first post',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // Reporting post with user 0
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 0)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    // Should not create report because same device ID with user 0
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 2)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    let reports = await PostReport.find({ reportedQuestion: q1Id });
    expect(reports.length).to.equal(1);

    // Reporting post with user 3
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 3)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    reports = await PostReport.find({ reportedQuestion: q1Id });
    expect(reports.length).to.equal(2);

    // sending invalid question ID
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 3)
      .send({ questionId: 123 });
    expect(res.status).to.equal(422);
  });
});

describe('common tests', () => {
  it('question tests', async () => {
    for (let uid = 1; uid <= REPORTS_UNTIL_DELETION; uid++) {
      res = await request(app)
        .get('/v1/user')
        .set('authorization', uid.toString());
      expect(res.status).to.equal(200);
    }

    // empty all questions
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.questions).to.eql([]);

    // post question 1
    let createdQuestion = await createQuestion({
      createdAt: new Date(2021, 5, 20, 4, 5, 0, 0),
      text: 'question 1',
      interestName: 'questions',
    });

    await Question.updateSearchFields(createdQuestion._id)

    // get all questions
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    const question1Id = res.body.questions[0]._id;
    const q1 = {
      _id: question1Id,
      createdAt: res.body.questions[0].createdAt,
      createdBy: null,
      images: [],
      interest: null,
      interestName: 'questions',
      text: 'question 1',
      numComments: 0,
      numLikes: 0,
      isDeleted: false,
      isEdited: false,
      hasUserLiked: false,
      hasUserSaved: false,
      language: 'en',
      webId: res.body.questions[0].webId,
      url: res.body.questions[0].url,
      linkedKeywords: [],
      linkedExploreKeywords: ['question'],
      linkedPillarKeywords: [],
      linkedCategories: [],
      linkedSubcategories: [],
      linkedProfiles: [],
      hashtags: ['questions'],
    };
    expect(res.body.questions[0]).to.eql(q1);

    // get question by id
    res = await request(app)
      .get('/v1/question')
      .query({ questionId: question1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.question).to.eql(q1);

    // user 1 likes question 1
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', '1')
      .send({ questionId: question1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question')
      .query({ questionId: question1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.question.numLikes).to.equal(1);
    expect(res.body.question.hasUserLiked).to.equal(true);

    // user 1 tries to like question 1 again
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', '1')
      .send({ questionId: question1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question')
      .query({ questionId: question1Id })
      .set('authorization', '2');
    expect(res.status).to.equal(200);
    expect(res.body.question.numLikes).to.equal(1);

    // user 2 likes question 1
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', '2')
      .send({ questionId: question1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question')
      .query({ questionId: question1Id })
      .set('authorization', '2');
    expect(res.status).to.equal(200);
    expect(res.body.question.numLikes).to.equal(2);

    // user 2 tries to like question 1 again
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', '2')
      .send({ questionId: question1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question')
      .query({ questionId: question1Id })
      .set('authorization', '2');
    expect(res.status).to.equal(200);
    expect(res.body.question.numLikes).to.equal(2);

    // user 1 unlikes question 1
    res = await request(app)
      .patch('/v1/question/unlike')
      .set('authorization', '1')
      .send({ questionId: question1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question')
      .query({ questionId: question1Id })
      .set('authorization', '2');
    expect(res.status).to.equal(200);
    expect(res.body.question.numLikes).to.equal(1);

    // user 1 tries to unlike question 1 again
    res = await request(app)
      .patch('/v1/question/unlike')
      .set('authorization', '1')
      .send({ questionId: question1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question')
      .query({ questionId: question1Id })
      .set('authorization', '2');
    expect(res.status).to.equal(200);
    expect(res.body.question.numLikes).to.equal(1);

    // user 2 unlikes question 1
    res = await request(app)
      .patch('/v1/question/unlike')
      .set('authorization', '2')
      .send({ questionId: question1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question')
      .query({ questionId: question1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.question.numLikes).to.equal(0);

    // user 2 posts question 2
    await createQuestion({
      createdAt: new Date(2021, 5, 21, 4, 5, 0, 0),
      text: 'question 2',
      interestName: 'questions',
    });

    // get all questions (2 questions now)
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(2);
    expect((new Date(res.body.questions[0].createdAt).getTime())).to.equal((new Date(2021, 5, 21, 4, 5, 0, 0)).getTime());
    expect(res.body.questions[0].text).to.equal('question 2');
    expect(res.body.questions[0].numLikes).to.equal(0);
    expect(res.body.questions[0].isDeleted).to.equal(false);
    expect(res.body.questions[0].hasUserLiked).to.equal(false);
    expect(res.body.questions[1].hasUserLiked).to.equal(false);

    // user 1 likes question 1
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', '1')
      .send({ questionId: question1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.questions[0].hasUserLiked).to.equal(false);
    expect(res.body.questions[1].hasUserLiked).to.equal(true);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '2');
    expect(res.status).to.equal(200);
    expect(res.body.questions[0].hasUserLiked).to.equal(false);
    expect(res.body.questions[1].hasUserLiked).to.equal(false);

    // future question
    const q3 = await createQuestion({
      createdAt: Date.now() + 90 * 24 * 60 * 60 * 1000, // 3 months
      text: 'question 3',
      interestName: 'questions',
    });

    // the future question should not be returned
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(2);

    // like the future question
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', '1')
      .send({ questionId: q3._id });
    expect(res.status).to.equal(200);

    console.log(await Question.findById(q3._id));
  });

  /*
  it('question views test', async function () {

    ////create user 1
    res = await request(app)
      .get('/v1/user')
      .set('authorization', '1')
    expect(res.status).to.equal(200);

    ////set version 1.11.37
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.11.37' })
    expect(res.status).to.equal(200);

    // post question 1
    await createQuestion({
      createdAt: new Date(2021, 5, 20, 4, 5, 0, 0),
      text: 'question 1',
      interestName: 'questions'
    });

    // get all questions
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    let question1Id = res.body.questions[0]._id;
    let q1 = {
      _id: question1Id,
      createdAt: res.body.questions[0].createdAt,
      createdBy: null,
      interest: null,
      interestName: 'questions',
      text: "question 1",
      numComments: 0,
      numLikes: 0,
      numViews: 0,
      isDeleted: false,
      isEdited: false,
      hasUserLiked: false,
      hasUserSaved: false,
      language: 'en',
      webId: res.body.questions[0].webId,
      url: res.body.questions[0].url,
    }

    // view count should be incremented
    res = await request(app)
      .get('/v1/question')
      .query({ questionId: question1Id })
      .set('authorization', '1')
    expect(res.status).to.equal(200);
    q1.numViews++;
    expect(res.body.question).to.eql(q1);

    // viewing question again does not increment again
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]).to.eql(q1);

    ////create user 2
    res = await request(app)
      .get('/v1/user')
      .set('authorization', '2')
    expect(res.status).to.equal(200);

    ////set version 1.11.37
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.11.37' })
    expect(res.status).to.equal(200);

    // get all questions to view the post once
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '2');
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]).to.eql(q1);

    // get all questions
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '2');
    expect(res.status).to.equal(200);
    q1.numViews++;
    expect(res.body.questions[0]).to.eql(q1);

    // viewing question again does not increment again
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '2');
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]).to.eql(q1);
  });
  */
});

describe('banned keywords', async () => {
  beforeEach(async () => {
    // create two users
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
  });

  it('not banned keywords', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'Who can delete the error',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // post should not be banned for keyword 'tit'
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'Cuales son tus gustos más cuestionables?',
        text: `Hagan su hear us out o como se escriba \nEmpiezo yo \nNémesis \nPyramid head \nEl papá de Bambi \nEl hombre bicentenario \nLa voz de candy crush \nKind Kong \nDepredador \nMousse de Ranma ½\nYakotsu \nTitán acorazado \nRyuk \nUsop\nTakeo goda \nGyomei Himejima\nThel vadam\nMr misterio de shuumatsu no valkirye \nRika de jujutsu kaisen \nZatsu\n\nBueno no recuerdo ninguno más`,
      });
    expect(res.status).to.equal(200);

    post = await Question.findById(res.body._id);
    expect(post.banned).to.equal(false);
  });

  it('banned keywords on title', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'I want to delete This app okay',
      });
    expect(res.status).to.equal(200);

    // user 0 can see the post, user 1 cannot
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('split keywords', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'Okay',
        text: 'Many of the profiles are fake',
      });
    expect(res.status).to.equal(200);

    // user 0 can see the post, user 1 cannot
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('banned keywords on poll options', async () => {
    // create poll with banned keyword
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title',
        poll: ['0', '1', 'censor'],
      });
    expect(res.status).to.equal(200);

    // user 0 can see the post, user 1 cannot
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('translated keywords - language without word boundary', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'なにくそ',
        language: 'ja',
      });
    expect(res.status).to.equal(200);

    // user 0 can see the post, user 1 cannot
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'ja' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .query({ language: 'ja' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('translated keywords - language with word boundary', async () => {
    // full match "am"
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'am',
        language: 'tr',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'tr' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .query({ language: 'tr' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('translated keywords - language with word boundary', async () => {
    metricsLib.getPopularLanguages.restore();
    sinon.stub(metricsLib, 'getPopularLanguages').returns(['tr']);

    // partial match "am" should not count
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'tam',
        language: 'tr',
      });
    expect(res.status).to.equal(200);

    // should not be banned
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'tr' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .query({ language: 'tr' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('post moderation with deepseek', async () => {
    fakeOpenaiClient.chat = {
      completions: {
        async create(params) {
          const question = await Question.find().sort({ createdAt: -1 }).limit(1).lean();
          const content = question[0].createdBy === '0'
            ? `[{"postId": "${question[0]._id}","ban":false,"explanation":"The post does not contain any negative criticism, complaints, or portrayal of Boo or Boo Infinity."}]`
            : `[{"postId": "${question[0]._id}", "ban": true, "explanation": "The post mentions something about subscription, which portrays Boo in a negative light."}]`;

          return {
            usage: {
              prompt_tokens: 10,
              completion_tokens: 10,
            },
            choices: [{
              message: {
                content,
              },
            }],
          };
        },
      },
    };

    // should not be banned
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'testing this app keyword',
        language: 'en',
      });
    expect(res.status).to.equal(200);

    await new Promise((resolve) => setTimeout(resolve, 100));

    let question = await Question.findById(res.body._id);
    expect(question.banned).to.equal(false);

    let moderation = await PostModeration.findOne({ question: question._id });
    expect(moderation.openai.model).to.equal('deepseek-ai/DeepSeek-V3');

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .query({ language: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // should be banned
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'chess',
        title: 'testing subscription keyword',
        language: 'en',
      });
    expect(res.status).to.equal(200);
    await new Promise((resolve) => setTimeout(resolve, 100));

    question = await Question.findById(res.body._id);
    expect(question.banned).to.equal(true);

    moderation = await PostModeration.findOne({ question: question._id });
    expect(moderation.openai.model).to.equal('deepseek-ai/DeepSeek-V3');

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .query({ language: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(2);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });
});

describe('language detection', async () => {
  beforeEach(async () => {

    metricsLib.getPopularLanguages.restore();
    sinon.stub(metricsLib, 'getPopularLanguages').returns([]);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
  });

  it('language matches', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title 1',
        language: 'en',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.detectedLanguage).to.equal('en');
    expect(question.languageMismatch).to.equal(false);
    expect(question.detectedLanguageConfidence).to.equal(1);
    expect(question.banned).to.equal(false);
  });

  it('language does not match', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title 2',
        language: 'fr',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.detectedLanguage).to.equal('en');
    expect(question.languageMismatch).to.equal(true);
    expect(question.detectedLanguageConfidence).to.equal(1);
    expect(question.banned).to.equal(true);
  });

  it('detected language german', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'de',
        language: 'en',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.detectedLanguage).to.equal('de');
    expect(question.languageMismatch).to.equal(true);
    expect(question.detectedLanguageConfidence).to.equal(1);
    expect(question.banned).to.equal(true);
  });

  it('detected language undefined', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'und',
        language: 'en',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.detectedLanguage).to.equal('und');
    expect(question.languageMismatch).to.equal(true);
    expect(question.detectedLanguageConfidence).to.equal(1);
    expect(question.banned).to.equal(false);
  });

  it('detected language Chinese', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'zh-CN',
        language: 'zh',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.detectedLanguage).to.equal('zh');
    expect(question.languageMismatch).to.equal(false);
    expect(question.detectedLanguageConfidence).to.equal(1);
    expect(question.banned).to.equal(false);
  });

  it('language not specified', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title 2',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.detectedLanguage).to.equal('en');
    expect(question.languageMismatch).to.equal(false);
    expect(question.detectedLanguageConfidence).to.equal(1);
    expect(question.banned).to.equal(false);
  });

  it('check language - mismatch', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title title',
        text: 'title title',
        language: 'fr',
        checkLanguage: true,
      });
    expect(res.status).to.equal(409);

    question = await Question.find();
    expect(question.length).to.equal(0);

    // post anyway
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title title',
        text: 'title title',
        language: 'fr',
        checkLanguage: false,
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.language).to.equal('fr');
    expect(question.detectedLanguage).to.equal('en');
    expect(question.languageMismatch).to.equal(true);
    expect(question.detectedLanguageConfidence).to.equal(1);
    expect(question.banned).to.equal(true);

    mismatches = await LanguageMismatch.find();
    expect(mismatches.length).to.equal(1);
    mismatch = mismatches[0];
    expect(mismatch.user).to.equal('0');
    expect(mismatch.type).to.equal('post');
    expect(mismatch.title).to.equal('title title');
    expect(mismatch.text).to.equal('title title');
    expect(mismatch.targetLanguage).to.equal('fr');
    expect(mismatch.detectedLanguage).to.equal('en');
    expect(mismatch.confidence).to.equal(1);
  });

  it('check language - no mismatch', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title title',
        text: 'title title',
        language: 'en',
        checkLanguage: true,
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.language).to.equal('en');
    expect(question.detectedLanguage).to.equal('en');
    expect(question.languageMismatch).to.equal(false);
    expect(question.detectedLanguageConfidence).to.equal(1);
    expect(question.banned).to.equal(false);
  });

  it('check language - under 0.9 confidence', async () => {

    googleTranslate.detect.restore();
    sinon.stub(googleTranslate, 'detect').callsFake(async (params) => {
      return [ {
        confidence: 0.85,
        language: 'fr',
      } ];
    });

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title title',
        text: 'title title',
        language: 'fr',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.language).to.equal('fr');
    expect(question.detectedLanguage).to.equal('fr');
    expect(question.languageMismatch).to.equal(false);
    expect(question.detectedLanguageConfidence).to.equal(0.85);
    expect(question.banned).to.equal(true);
  });

  it('check language - Danish confidence 0.85', async () => {

    googleTranslate.detect.restore();
    sinon.stub(googleTranslate, 'detect').callsFake(async (params) => {
      return [ {
        confidence: 0.85,
        language: 'da',
      } ];
    });

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title title',
        text: 'title title',
        language: 'da',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.language).to.equal('da');
    expect(question.detectedLanguage).to.equal('da');
    expect(question.languageMismatch).to.equal(false);
    expect(question.detectedLanguageConfidence).to.equal(0.85);
    expect(question.banned).to.equal(false);
  });

  it('check language - Danish confidence 0.75', async () => {

    googleTranslate.detect.restore();
    sinon.stub(googleTranslate, 'detect').callsFake(async (params) => {
      return [ {
        confidence: 0.75,
        language: 'da',
      } ];
    });

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title title',
        text: 'title title',
        language: 'da',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.language).to.equal('da');
    expect(question.detectedLanguage).to.equal('da');
    expect(question.languageMismatch).to.equal(false);
    expect(question.detectedLanguageConfidence).to.equal(0.75);
    expect(question.banned).to.equal(true);
  });

  it('skip language check for user app language', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ locale: 'fr' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title 2',
        language: 'fr',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.detectedLanguage).to.equal();
    expect(question.languageMismatch).to.equal();
    expect(question.detectedLanguageConfidence).to.equal();
    expect(question.banned).to.equal(false);

    mismatches = await LanguageMismatch.find();
    expect(mismatches.length).to.equal(0);
  });

  it('skip language check for user device language', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ deviceLanguage: 'fr' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title 2',
        language: 'fr',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.detectedLanguage).to.equal();
    expect(question.languageMismatch).to.equal();
    expect(question.detectedLanguageConfidence).to.equal();
    expect(question.banned).to.equal(false);

    mismatches = await LanguageMismatch.find();
    expect(mismatches.length).to.equal(0);
  });

  it('skip language check for sign languages', async () => {

    googleTranslate.detect.restore();
    sinon.stub(googleTranslate, 'detect').callsFake(async (params) => {
      return [ {
        confidence: 0.95,
        language: 'fr',
      } ];
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title 2',
        language: 'ase',
      });
    expect(res.status).to.equal(200);

    question = await Question.findById(res.body._id);
    expect(question.language).to.equal('ase');
    expect(question.detectedLanguage).to.equal();
    expect(question.languageMismatch).to.equal();
    expect(question.detectedLanguageConfidence).to.equal();
    expect(question.banned).to.equal(false);

    mismatches = await LanguageMismatch.find();
    expect(mismatches.length).to.equal(0);
  });

});

const setCoins = async (userId, coins) => {
  await UserMetadata.updateOne({ user: userId }, { $set: { coins } });
  expect((await fetchCoinData(userId)).coins).to.eql(coins);
};

const getQuestionDoc = async (qId) => JSON.parse(JSON.stringify(await Question.findById(qId)));

it('boost question', async () => {
  let res = await initApp(0);
  const interestId = res.interests[0]._id;
  await setUserInterests(0, { interestIds: [interestId] });

  const boostQuestion = (userId, params) => request(app)
    .patch('/v1/question/boost')
    .set('authorization', userId)
    .send(params);

  // invalid input
  res = await boostQuestion(0, '1');
  expect(res.status).to.eql(422);
  res = await boostQuestion(0, {
    questionId: '1',
    price: coinsConstants.boostPostCost,
  });
  expect(res.status).to.eql(422);

  // user 0 posts question
  res = await postQuestion(0, {
    interestId,
    title: 'title',
  });

  const qIds = [res._id];

  // before version 1.11.61 isBoosted not sent
  expect(res.isBoosted).to.eql(undefined);
  res = await getQuestion(0, { questionId: qIds[0] });
  expect(res.question._id).to.eql(qIds[0]);
  expect(res.question.isBoosted).to.eql(undefined);
  res = await getAllQuestions(0, { interestId });
  expect(res.questions[0]._id).to.eql(qIds[0]);
  expect(res.questions[0].isBoosted).to.eql(undefined);
  res = await getQuestionFeed(0, {});
  expect(res.questions[0]._id).to.eql(qIds[0]);
  expect(res.questions[0].isBoosted).to.eql(undefined);

  // create user 1
  await initApp(1);

  const qDoc = await getQuestionDoc(qIds[0]);

  // boost with wrong price
  res = await boostQuestion(0, {
    questionId: qIds[0],
    price: coinsConstants.boostPostCost - 10,
  });
  expect(res.status).to.eql(409);

  // no change to question in db
  expect(await getQuestionDoc(qIds[0])).to.eql(qDoc);

  // notFound error if boosted by user other than owner
  res = await boostQuestion(1, {
    questionId: qIds[0],
    price: coinsConstants.boostPostCost,
  });
  expect(res.status).to.eql(404);

  // no change to question in db
  expect(await getQuestionDoc(qIds[0])).to.eql(qDoc);

  await fetchCoinData(0);// to get any pending coin rewards
  await setCoins(0, coinsConstants.boostPostCost - 10);

  // forbidden error -not enough coins
  res = await boostQuestion(0, {
    questionId: qIds[0],
    price: coinsConstants.boostPostCost,
  });
  expect(res.status).to.eql(403);
  expect(await getQuestionDoc(qIds[0])).to.eql(qDoc);// no change to Doc

  const verifyBoostPostMetrics = async (userId, total, daily) => {
    const userDoc = await User.findOne({ _id: userId });
    expect(userDoc.metrics.numPostsBoosted).to.eql(total);
    expect(userDoc.currentDayMetrics.numPostsBoosted).to.eql(daily);
  };

  await verifyBoostPostMetrics(0, 0, 0);

  await setCoins(0, coinsConstants.boostPostCost);

  res = await boostQuestion(0, {
    questionId: qIds[0],
    price: coinsConstants.boostPostCost,
  });

  expect(res.status).to.eql(200);
  expect(res.body.coinsRemaining).to.eql(0);

  res = await fetchCoinData(0);
  expect(res.coins).to.eql(0);// coins deducted

  const updatedQDoc = await getQuestionDoc(qIds[0]);
  expect(updatedQDoc.isBoosted).to.eql(true);

  await setCoins(0, coinsConstants.boostPostCost + 10);

  // user attempts to boost again
  res = await boostQuestion(0, {
    questionId: qIds[0],
    price: coinsConstants.boostPostCost,
  });
  expect(res.status).to.eql(200);
  expect(res.body.coinsRemaining).to.eql(coinsConstants.boostPostCost + 10);

  res = await fetchCoinData(0);
  expect(res.coins).to.eql(coinsConstants.boostPostCost + 10);// coins not deducted

  res = await initApp(0, { appVersion: '1.11.61' });// bump appVersion to 1.11.61

  // checking isBoosted key again
  res = await getQuestion(0, { questionId: qIds[0] });
  expect(res.question._id).to.eql(qIds[0]);
  expect(res.question.isBoosted).to.eql(true);
  res = await getAllQuestions(0, { interestId });
  expect(res.questions[0]._id).to.eql(qIds[0]);
  expect(res.questions[0].isBoosted).to.eql(true);
  res = await getQuestionFeed(0, {});
  expect(res.questions[0]._id).to.eql(qIds[0]);
  expect(res.questions[0].isBoosted).to.eql(true);

  await verifyBoostPostMetrics(0, 1, 1);

  // user 0 posts another question
  res = await postQuestion(0, {
    interestId,
    title: 'title2',
  });
  expect(res.isBoosted).to.eql(false);
  qIds.push(res._id);

  res = await getQuestion(0, { questionId: qIds[1] });
  expect(res.question._id).to.eql(qIds[1]);
  expect(res.question.isBoosted).to.eql(false);

  // 3 likes for qId[1], 2 likes for qId[0]
  for (let i = 2; i <= 5; i++) {
    await initApp(i);
  }
  for (let i = 2; i < 5; i++) {
    await likeQuestion(i, { questionId: qIds[1] });
  }
  for (let i = 2; i < 4; i++) {
    await likeQuestion(i, { questionId: qIds[0] });
  }

  const allScoreSortTypes = ['popular', 'topAllTime', 'topYear', 'topMonth', 'topWeek'];// rising not included because handles post in a certain score range
  for (let i = 0; i < allScoreSortTypes.length; i++) {
    const isAffectedSortType = allScoreSortTypes[i] === 'popular';
    const first = (isAffectedSortType ? qIds[0] : qIds[1]).valueOf();
    const last = (isAffectedSortType ? qIds[1] : qIds[0]).valueOf();
    res = await getQuestionFeed(0, { sort: allScoreSortTypes[i] });
    expect(res.questions.length).to.eql(2);
    expect(res.questions[0]._id).to.eql(first);
    expect(res.questions[0].isBoosted).to.eql(isAffectedSortType);
    expect(res.questions[1]._id).to.eql(last);
    expect(res.questions[1].isBoosted).to.eql(!isAffectedSortType);
    res = await getAllQuestions(0, { interestId, sort: allScoreSortTypes[i] });
    expect(res.questions.length).to.eql(2);
    expect(res.questions[0]._id).to.eql(first);
    expect(res.questions[0].isBoosted).to.eql(isAffectedSortType);
    expect(res.questions[1]._id).to.eql(last);
    expect(res.questions[1].isBoosted).to.eql(!isAffectedSortType);
  }

  await verifyBoostPostMetrics(0, 1, 1);

  const userDoc = await User.findOne({ _id: 0 });
  userDoc.metrics.currentDayResetTime = userDoc.metrics.currentDayResetTime - 24 * 60 * 60 * 1000;
  await userDoc.save();
  await userDoc.resetCurrentDayMetricsIfNeeded();

  await verifyBoostPostMetrics(0, 1, 0);

  res = await fetchCoinData(0);// to receive reward coins
  await setCoins(0, coinsConstants.boostPostCost + 10);

  // user attempts to boost again
  res = await boostQuestion(0, {
    questionId: qIds[1],
    price: coinsConstants.boostPostCost,
  });
  expect(res.status).to.eql(200);
  expect(res.body.coinsRemaining).to.eql(10);// remaining coins

  res = await fetchCoinData(0);// to receive reward coins
  expect(res.coins).to.eql(10);

  await verifyBoostPostMetrics(0, 2, 1);
});

it('ignite question',async ()=>{
  let res =await initApp(0,{appVersion:'1.11.50'});
  const igniteAward = res.coinProducts.awards.find((x) => x.id == 'ignite');
  const interestId = res.interests[0]._id;

  // user 0 posts question
  res = await postQuestion(0, {
    interestId,
    title: 'title',
  });

  const qIds=[res._id];

  //create user 1
  await initApp(1);

  // user 0 posts another question
  res = await postQuestion(0, {
    interestId,
    title: 'title',
  });
  qIds.push(res._id);

  // 3 likes for qId[1], 2 likes for qId[0]
  for (let i = 2; i < 5; i++) {
    await initApp(i);
    await likeQuestion(i, { questionId: qIds[1] });
  }
  for (let i = 2; i < 4; i++) {
    await likeQuestion(i, { questionId: qIds[0] });
  }

  //give user 1 sufficient coins to send ignite award
  await fetchCoinData(1);//to get previous rewards
  await setCoins(1,igniteAward.price);

  //user 1 awards ignite award to qId[0]
  await awardQuestion(1, {
    postId: qIds[0],
    awardId: igniteAward.id,
    price: igniteAward.price,
    message: '',
  });

  //ignite only affects popular sort
  const allScoreSortTypes = ['popular', 'topAllTime', 'topYear', 'topMonth', 'topWeek'];// rising not included because handles post in a certain score range
  for (let i = 0; i < allScoreSortTypes.length; i++) {
    const isAffectedSortType = allScoreSortTypes[i] === 'popular';
    const first = (isAffectedSortType ? qIds[0] : qIds[1]).valueOf();
    const last = (isAffectedSortType ? qIds[1] : qIds[0]).valueOf();
    res = await getQuestionFeed(0, { sort: allScoreSortTypes[i] });
    expect(res.questions.length).to.eql(2);
    expect(res.questions[0]._id).to.eql(first);
    expect(res.questions[1]._id).to.eql(last);
    res = await getAllQuestions(0, { interestId, sort: allScoreSortTypes[i] });
    expect(res.questions.length).to.eql(2);
    expect(res.questions[0]._id).to.eql(first);
    expect(res.questions[1]._id).to.eql(last);
  }


  //give user 0 sufficient coins to boostQuestion
  await fetchCoinData(0);//to get previous rewards
  await setCoins(0, coinsConstants.boostPostCost);

  let dbDoc = await getQuestionDoc(qIds[0]);
  const oldScore=Math.floor(dbDoc.score/10);

  res = await boostQuestion(0, {
    questionId: qIds[0],
    price: coinsConstants.boostPostCost,
  });

  dbDoc = await getQuestionDoc(qIds[0]);
  expect(Math.floor(dbDoc.score/10)).to.eq(oldScore);//ignite and boost don't stack
});


it('question userAttributes', async function () {

  await initApp(1);
  await initApp(2);

  await setUserBirthday(1,{
    year: new Date().getFullYear() - 30,
    month: 1,
    day: 1,
  });
  await setPersonality(1, { mbti: 'ISTJ' });
  await setPersonality(2, { mbti: 'INFP' });
  await setUserPreferences(2,{maxAge:19,gender:['female']});
  await setUserPreferences(1,{minAge:19,gender:['male']});
  await setLocation(1, [-1.25, 51.75]);
  await setEnneagram(2,{enneagram:'1w2'});
  await setEnneagram(2,{enneagram:'1w2'});

  res = await User.find();
  expect(res.length).to.eql(2);
  expect(res[0]._id).to.eql('1');
  expect(res[1]._id).to.eql('2');
  res[0].verification.status='verified';
  await res[0].save();


  const userAttributes = res.map((user) => JSON.parse(JSON.stringify({
    age: user.age,
    minAge: user.preferences.minAge,
    maxAge: user.preferences.maxAge,
    genderPreferenceHash: user.genderPreferenceHash,
    status: user.verification.status,
    mbti: user.personality.mbti,
    countryCode: user.countryCode,
    city: user.city,
    state: user.state,
    enneagram: user.enneagram,
    horoscope: user.horoscope,
    latitude2: user.latitude2,
    longitude2: user.longitude2,
  })));

  res = await postQuestion(1, {
    interestName: 'kpop',
    title: `title1`,
    text: `text1`,
  });

  const qId1 = res._id;
  res = await Question.findOne({ _id: qId1 }, { userAttributes: 1 });
  //userAttributes saved to question
  expect(JSON.parse(JSON.stringify(res.userAttributes))).to.eql(userAttributes[0]);


  res = await postQuestion(2, {
    interestName: 'kpop',
    title: `title2`,
    text: `text2`,
  });

  const qId2 = res._id;
  res = await Question.findOne({ _id: qId2 }, { userAttributes: 1 });
  //userAttributes saved to question
  expect(JSON.parse(JSON.stringify(res.userAttributes))).to.eql(userAttributes[1]);

  res = await postQuestion(1, {
    interestName: 'kpop',
    title: `title3`,
    text: `text3`,
  });
  const qId3 = res._id;
  res = await Question.findOne({ _id: qId3 }, { userAttributes: 1 });
  //userAttributes saved to question
  expect(JSON.parse(JSON.stringify(res.userAttributes))).to.eql(userAttributes[0]);

  await setPersonality(2, { mbti: 'ENTJ' });
  userAttributes[1].mbti = 'ENTJ';

  res = await Question.find({}, { userAttributes: 1, createdBy: 1 });
  expect(res.length).to.eql(3);
  expect(res[0].createdBy).to.eql('1');
  expect(res[1].createdBy).to.eql('2');
  expect(res[2].createdBy).to.eql('1');
  //updated attributes of correct user only
  expect(JSON.parse(JSON.stringify(res[0].userAttributes))).to.eql(userAttributes[0]);
  expect(JSON.parse(JSON.stringify(res[1].userAttributes))).to.eql(userAttributes[1]);
  expect(JSON.parse(JSON.stringify(res[2].userAttributes))).to.eql(userAttributes[0]);


  await Question.updateMany({}, { $unset: { userAttributes: 1 } });
  res = await Question.find({}, { userAttributes: 1, createdBy: 1 });
  expect(res[0].userAttributes).to.eql(undefined);
  expect(res[1].userAttributes).to.eql(undefined);
  expect(res[2].userAttributes).to.eql(undefined);



  ////BackFill User Attributes Script
  await backFillUserAttributes();
  res = await Question.find({}, { userAttributes: 1, createdBy: 1 });
  expect(res.length).to.eql(3);
  expect(res[0].createdBy).to.eql('1');
  expect(res[1].createdBy).to.eql('2');
  expect(res[2].createdBy).to.eql('1');

  userAttributes[0].enneagram = null;
  userAttributes[0].genderPreferenceHash = null;

  userAttributes[1].countryCode = null;
  userAttributes[1].city = null;
  userAttributes[1].state = null;
  userAttributes[1].genderPreferenceHash = null;
  userAttributes[1].horoscope = null;
  userAttributes[1].latitude2 = null;
  userAttributes[1].longitude2 = null;

  //updated attributes of users
  expect(JSON.parse(JSON.stringify(res[0].userAttributes))).to.eql(userAttributes[0]);
  expect(JSON.parse(JSON.stringify(res[1].userAttributes))).to.eql(userAttributes[1]);
  expect(JSON.parse(JSON.stringify(res[2].userAttributes))).to.eql(userAttributes[0]);

});

it('dndPost', async () => {
  const setProfileNotifications = async function (userId, params) {
    return await request(app)
    .patch('/v1/user/profile/notifications')
    .set('authorization', userId)
    .send(params);
  }

  await initApp(0, { appVersion: '1.11.75' });
  await setFcmToken(0,'token');
  await initApp(1);
  await sendUserLike(1, { user: '0' });
  await approveUser(0, { user: '1' });

  res = await getIndividualChat(0, { user: 1 });
  expect(res.chat.dndPost).to.eql(false);//dndPost false by default
  expect(res.chat.dndMessage).to.eql(false);//dndMessage false by default


  res = await setProfileNotifications(0, { user: '1', dndPost: 'asdf' });
  expect(res.status).to.eql(422);//invalid input error
  expect(res.body).to.eql({});

  res = await setProfileNotifications(0, { user: '1', dndPost: true });
  expect(res.status).to.eql(200);//success
  expect(res.body).to.eql({});

  //user notification settings updated
  res = await getIndividualChat(0, { user: '1' });
  expect(res.chat.dndPost).to.eql(true);
  expect(res.chat.dndMessage).to.eql(false);

  reset();

  await postQuestion(1, {
    interestName: 'kpop',
    title: `title1`,
    text: `text1`,
  });

  resetTime = Date.now();
  await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
  expect(notifs.numSent).to.equal(0);//no notifications received

  //disable dndPost
  res = await setProfileNotifications(0, { user: '1', dndPost: false });
  expect(res.status).to.eql(200);//successs
  expect(res.body).to.eql({});

  //no change for user 1
  res = await getIndividualChat(0, { user: '1' });
  expect(res.chat.dndMessage).to.eql(false);
  expect(res.chat.dndPost).to.eql(false);

  reset();

  await postQuestion(1, {
    interestName: 'kpop',
    title: `title2`,
    text: `text2`,
  });

  resetTime = Date.now();
  await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
  expect(notifs.numSent).to.equal(1);// notifications received
  expect(notifs.recent.token).to.equal('token');
  expect(notifs.recent.notification.body).to.equal('title2');
});

describe('faceDetected in post images', ()=>{
  let ogDetectFaces;
  beforeEach(() => {
    ogDetectFaces = fakeRekognition.detectFaces;
  });
  afterEach(() => {
    fakeRekognition.detectFaces = ogDetectFaces;
  });
  for(let language of ['id','es','en']){
    it(`faceDetected moderation for language- ${language}` ,async()=>{

      let res=await initApp(0);//create a user
      const interestId = res.interests[0]._id;

      //post a question
      res=await postQuestion(0,{
        interestId: interestId,
        title: 'title1',
        text: 'text1',
        language,
      });
      await postQuestionImage(0, { questionId: res._id });//add image to question
      await waitMs(10);

      let questionData = await Question.findOne({ _id: res._id }, { nonDecayedScore: 1, faceDetected: 1 });
      expect(questionData.nonDecayedScore).greaterThan(0);
      expect(questionData.faceDetected).eql(undefined);

      fakeRekognition.detectFaces = function (params) {
        const impl = function (resolve, reject) {
          console.log('Rekognition : detect-faces')
          resolve({ FaceDetails: [{ Confidence: 99.1 }] });
        };
        return {
          promise: () => new Promise(impl),
        };
      };
      await postQuestionImage(0, { questionId: res._id });//image with face detected
      await waitMs(10);
      questionData = await Question.findOne({ _id: res._id }, { nonDecayedScore: 1, faceDetected: 1 });

      expect(questionData.nonDecayedScore).greaterThan(0);
      expect(questionData.faceDetected).to.eql(true);

      await deleteQuestionImage(0,{questionId:res._id});
      questionData = await Question.findOne({ _id: res._id }, { faceDetected: 1 });
      expect(questionData.faceDetected).to.eql(undefined);
    });
  }
});

it('get question invalid input', async () => {
  res = await request(app)
    .get('/web/question')
    .query({ questionId: 'invalid' })
  expect(res.status).to.equal(422);
});

it('question view data', async () => {
  await initApp(0);
  await initApp(1);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'chess',
      title: 'first post',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  res = await request(app)
    .put('/v1/question-view-data')
    .set('authorization', '0')
    .send({ questions: [ { questionId: q1Id, fieldsToIncrement: { numSecondsReadingComments: 1 } } ] })
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/question-view-data')
    .set('authorization', '0')
    .send({ questions: [ { questionId: q1Id, fieldsToIncrement: { numClicks: 1, numSecondsOnPost: 1, numSecondsReadingOnFeed: 1, numShares: 1 } } ] })
  expect(res.status).to.equal(200);

  docs = await QuestionViewData.find();
  expect(docs.length).to.equal(1);
  expect(docs[0].user).to.equal('0');
  expect(docs[0].numSecondsReadingComments).to.equal(1);
  expect(docs[0].numClicks).to.equal(1);
  expect(docs[0].numSecondsOnPost).to.equal(1);
  expect(docs[0].numSecondsReadingOnFeed).to.equal(1);
  expect(docs[0].numShares).to.equal(1);

  docs = await Question.find();
  expect(docs.length).to.equal(1);
  expect(docs[0].numSecondsReadingComments).to.equal(1);
  expect(docs[0].numClicks).to.equal(1);
  expect(docs[0].numSecondsOnPost).to.equal(1);
  expect(docs[0].numSecondsReadingOnFeed).to.equal(1);
  expect(docs[0].numShares).to.equal(1);
  expect(docs[0].hourlyEngagement[0].numSecondsReadingComments).to.equal(1);
  expect(docs[0].hourlyEngagement[0].numClicks).to.equal(1);
  expect(docs[0].hourlyEngagement[0].numSecondsOnPost).to.equal(1);
  expect(docs[0].hourlyEngagement[0].numSecondsReadingOnFeed).to.equal(1);
  expect(docs[0].hourlyEngagement[0].numShares).to.equal(1);

  user = await User.findById('0');
  expect(user.metrics.numPostShares).to.equal(1);
  expect(user.metrics.numPostClicks).to.equal(1);
  expect(user.metrics.numSecondsReadingOnFeed).to.equal(1);
  expect(user.metrics.numSecondsReadingComments).to.equal(1);
});

describe('alt text', async () => {
  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
  });

  it('basic functionality', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title 1',
        language: 'en',
        altText: 'image',
      });
    expect(res.status).to.equal(200);
    expect(res.body.altText).to.equal('image');
    q1Id = res.body._id;

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].altText).to.equal('image');

    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        title: 'title 1',
        altText: 'image2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].altText).to.equal('image2');
  });
});

it('auto-ban on report threshold - disabled', async () => {
  await initApp(0);
  await initApp(1);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'chess',
      title: 'first post',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  // set high number of likes
  q1 = await Question.findById(q1Id);
  q1.numLikes = 100;
  q1.usersThatReported = ['10', '11', '12', '13'];
  await q1.save();

  // not auto-banned
  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 1)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  q1 = await Question.findById(q1Id);
  expect(q1.banned).to.equal(false);

  // set low number of likes
  q1 = await Question.findById(q1Id);
  q1.numLikes = 0;
  q1.usersThatReported = ['10', '11', '12', '13'];
  await q1.save();

  // auto-banned - disabled
  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 1)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  q1 = await Question.findById(q1Id);
  expect(q1.banned).to.equal(false);
});

it('report with reason and explanation', async () => {
  await initApp(0);
  await initApp(1);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'chess',
      title: 'first post',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 1)
    .send({ questionId: q1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);
});

it('media upload pending', async () => {
  await initApp(0);
  await initApp(1);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'chess',
      title: 'first post',
      mediaUploadPending: true,
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  // post not visible yet
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  // finish uploading image
  res = await request(app)
    .post('/v1/question/image')
    .set('authorization', 0)
    .query({ questionId: q1Id })
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  // now post is visible
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
});

describe('multiple images', async () => {

  it('upload multiple images', async () => {
    await initApp(0);
    await initApp(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
      { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
    ]),

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .attach('images', validImagePath)  // Attach the second image
      .attach('images', validImagePath) // Attach more images if needed
      .field('imagesArray', images)
    expect(res.status).to.equal(200);
    console.log('upload res :', res.body)

    // now post is visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    console.log(JSON.stringify(res.body.questions, null, 2))

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].isEdited).to.not.equal(true)
  });

  it('upload multiple images, then delete all images', async () => {
    await initApp(0);
    await initApp(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
      { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
    ]),

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .attach('images', validImagePath)  // Attach the second image
      .attach('images', validImagePath) // Attach more images if needed
      .field('imagesArray', images)
    expect(res.status).to.equal(200);
    console.log('upload res :', res.body)

    // now post is visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    console.log(JSON.stringify(res.body.questions, null, 2))
    expect(res.body.questions[0].images.length).to.equal(3)

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].isEdited).to.not.equal(true)

    //try delete all images by send empty imagesArray should be failed
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .field('imagesArray', JSON.stringify([]))
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    console.log(JSON.stringify(res.body.questions, null, 2))
    expect(res.body.questions[0].images.length).to.equal(0)
  });

  it('only first image seen by from version below 1.13.68', async () => {
    await initApp(0);
    await initApp(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .send({ appVersion: '1.13.67' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
      { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
    ]),

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .attach('images', validImagePath)  // Attach the second image
      .attach('images', validImagePath) // Attach more images if needed
      .field('imagesArray', images)
    expect(res.status).to.equal(200);
    uploadRes = res.body
    console.log('upload res :', uploadRes)

    // now post is visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    console.log(JSON.stringify(res.body.questions, null, 2))

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .send({ appVersion: '1.13.67' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].isEdited).to.not.equal(true)

    // image & altText field from the old data structure should equal with the first image on images field
    expect(res.body.questions[0].image).to.equal(uploadRes.images[0].image)
    expect(res.body.questions[0].altText).to.equal(uploadRes.images[0].altText)
  });

  it('post question from 1.13.67, edit add more photo from version 1.13.68', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.67' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.67' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .send({ appVersion: '1.13.67' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    //upload single image useing old endpoint
    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    imageUploaded = res.body.image
    console.log('upload res old image :', imageUploaded)

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .send({ appVersion: '1.13.67' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].isEdited).to.not.equal(true)
    expect(res.body.questions[0].image).to.equal(imageUploaded)

    //init with versiion 1.13.68
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.68' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.68' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        title: 'edit post',
      });
    expect(res.status).to.equal(200);

    //edit question add 2 more image
    images = JSON.stringify([
      { type: 'existing', id: imageUploaded, altText: 'existing image' },
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
    ]),

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach new image 1
      .attach('images', validImagePath) // Attach new image 2
      .field('imagesArray', images)
    expect(res.status).to.equal(200);
    uploadRes = res.body
    console.log('upload res :', uploadRes)

    // now post is visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    console.log(JSON.stringify(res.body.questions, null, 2))


    // // image & altText field from the old data structure should equal with the first image on images field
    // expect(res.body.questions[0].image).to.equal(uploadRes.images[0].image)
    // expect(res.body.questions[0].altText).to.equal(uploadRes.images[0].altText)
  });

  it('upload one image using /images', async () => {
    await initApp(0);
    await initApp(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
    ]),

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .field('imagesArray', images)
    expect(res.status).to.equal(200);
    console.log('upload res :', res.body)

    // now post is visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    console.log(JSON.stringify(res.body.questions, null, 2))

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].isEdited).to.not.equal(true)
  });

  it('upload multiple images, imagesArray not provided', async () => {
    await initApp(0);
    await initApp(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .attach('images', validImagePath)  // Attach the second image
      .attach('images', validImagePath) // Attach more images if needed
    expect(res.status).to.equal(422);
    expect(res.error.text).to.equal('No images provided')
  });

  it('upload multiple images, invalid index', async () => {
    await initApp(0);
    await initApp(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
      { type: 'new', fileIndex: 99, altText: 'Inserted new image3' },
    ]),

    console.log('images :', typeof(images))

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .attach('images', validImagePath)  // Attach the second image
      .attach('images', validImagePath) // Attach more images if needed
      .field('imagesArray', images)
    expect(res.status).to.equal(422);
    expect(res.error.text).to.equal('New image at position 2 is missing or invalid.')
  });

  it('upload multiple images, duplicate', async () => {
    await initApp(0);
    await initApp(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'New image' },
      { type: 'new', fileIndex: 1, altText: 'Duplicate image' },
    ]),

    console.log('images :', typeof(images))

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .attach('images', validImagePath)  // Attach the second image
      .attach('images', validImagePath) // Attach more images if needed
      .field('imagesArray', images)
    expect(res.status).to.equal(422);
    expect(res.error.text).to.equal('File at index 1 is referenced multiple times.')
  });

  it('handle images re ordering', async () => {
    await initApp(0);
    await initApp(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
      { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
    ]),

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .attach('images', validImagePath)  // Attach the second image
      .attach('images', validImagePath) // Attach more images if needed
      .field('imagesArray', images)
    expect(res.status).to.equal(200);

    // now post is visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    console.log(JSON.stringify(res.body.questions, null, 2))
    const image0 = res.body.questions[0].images[0]
    const image1 = res.body.questions[0].images[1]
    const image2 = res.body.questions[0].images[2]

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    imagesNewOrder = JSON.stringify([
      { type: 'existing', id: image1.image, altText: image1.altText},
      { type: 'existing', id: image2.image, altText: image2.altText},
      { type: 'existing', id: image0.image, altText: image0.altText},
    ]),

    //reorder images
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .send({imagesArray : imagesNewOrder})
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].isEdited).to.equal(true)
    console.log('question : ', JSON.stringify(res.body.questions, null, 2))

    expect(res.body.questions[0].images[0].image).to.equal(image1.image);
    expect(res.body.questions[0].images[0].altText).to.equal(image1.altText);
    expect(res.body.questions[0].images[1].image).to.equal(image2.image);
    expect(res.body.questions[0].images[1].altText).to.equal(image2.altText);
    expect(res.body.questions[0].images[2].image).to.equal(image0.image);
    expect(res.body.questions[0].images[2].altText).to.equal(image0.altText);
  })

  it('handle images re ordering, add images in the middle, delete last images', async () => {
    await initApp(0);
    await initApp(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
      { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
    ]),

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .attach('images', validImagePath)  // Attach the second image
      .attach('images', validImagePath) // Attach more images if needed
      .field('imagesArray', images)
    expect(res.status).to.equal(200);

    // now post is visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    console.log(JSON.stringify(res.body.questions, null, 2))
    const image0 = res.body.questions[0].images[0]
    const image1 = res.body.questions[0].images[1]
    const image2 = res.body.questions[0].images[2]

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    imagesNewOrder = JSON.stringify([
      { type: 'existing', id: image1.image, altText: image1.altText},
      { type: 'new', fileIndex: 0, altText: 'add new image on edit' },
      { type: 'existing', id: image2.image, altText: image2.altText},
    ]),

    //reorder images, add new images delete
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)
      .field('imagesArray', imagesNewOrder)
    expect(res.status).to.equal(200);
    console.log('res upload', res.body)
    newImagesOrder = res.body.images

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].isEdited).to.equal(true)
    console.log('question : ', JSON.stringify(res.body.questions, null, 2))

    expect(res.body.questions[0].images[0].image).to.equal(newImagesOrder[0].image);
    expect(res.body.questions[0].images[0].altText).to.equal(newImagesOrder[0].altText);
    expect(res.body.questions[0].images[1].image).to.equal(newImagesOrder[1].image);
    expect(res.body.questions[0].images[1].altText).to.equal(newImagesOrder[1].altText);
    expect(res.body.questions[0].images[2].image).to.equal(newImagesOrder[2].image);
    expect(res.body.questions[0].images[2].altText).to.equal(newImagesOrder[2].altText);
  })

  it('delete existing image value, if its not exist on new images files', async() => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.68' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.68' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .send({ appVersion: '1.13.68' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .send({ appVersion: '1.13.68' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // finish uploading image
    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    imageUploaded = res.body.image
    console.log('upload res old image :', imageUploaded)

    // now post is visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .send({ appVersion: '1.13.68' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .send({ appVersion: '1.13.68' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].image).to.equal(imageUploaded)

    images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
      { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
    ]),

    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .attach('images', validImagePath)  // Attach the second image
      .attach('images', validImagePath) // Attach more images if needed
      .field('imagesArray', images)
    expect(res.status).to.equal(200);
    console.log('upload res :', res.body)

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .send({ appVersion: '1.13.68' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    console.log('questions res :', JSON.stringify(res.body, null, 2))
    expect(res.body.questions[0].image).to.be.undefined
  })

  it('delete question', async () => {
    await initApp(0);
    await initApp(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
      { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
    ]),

    // finish uploading image
    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)  // Attach the first image
      .attach('images', validImagePath)  // Attach the second image
      .attach('images', validImagePath) // Attach more images if needed
      .field('imagesArray', images)
    expect(res.status).to.equal(200);
    console.log('upload res :', res.body)

    // now post is visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    console.log(JSON.stringify(res.body.questions, null, 2))

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].isEdited).to.not.equal(true)

    res = await request(app)
      .delete(`/v1/question?questionId=${q1Id}`)
      .set('authorization', 0);
    expect(res.status).to.equal(200);

  })

  it('check moderation', async () => {
    await initApp(0);
    await initApp(1);
    await initApp(2);

    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 2)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // post not visible yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // Setup moderation response
    const rekognitionResponse = {
      isFlagged: false,
      detectionLabels: [],
    };

    const hiveResponse = {
      isFlagged: true,
      detectionLabels: [
        {
          ParentName: '',
          Name: 'general_nsfw',
          Confidence: 99.60970282554626,
        },
      ],
      flaggedModerationLabel: {
        ParentName: '',
        Name: 'general_nsfw',
        Confidence: 99.60970282554626,
      },
    };
    setMockImageModerationResponse(rekognitionResponse, hiveResponse);

    const images = JSON.stringify([
      { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
      { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
      { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
    ]);

    res = await request(app)
      .post('/v1/question/images')
      .set('authorization', 2)
      .query({ questionId: q1Id })
      .attach('images', validImagePath)
      .attach('images', validImagePath)
      .attach('images', validImagePath)
      .field('imagesArray', images);
    expect(res.status).to.equal(200);

    const question = await Question.findById(q1Id);
    const bannedReason = JSON.parse(question.bannedReason);
    expect(bannedReason.hive_v2).to.equal('general_nsfw');

    const modResults = await ImageModeration.find({});
    expect(modResults.length).to.equal(3);
    for (const result of modResults) {
      expect(result.isFlagged).to.equal(true);
      expect(result.serviceName).to.equal('Hive');
    }

    // Post is visible to creator
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // Post is flagged, so will not be visible
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });
});

describe('faceDetected in post with multiple images', ()=>{
  let ogDetectFaces;
  beforeEach(() => {
    ogDetectFaces = fakeRekognition.detectFaces;
  });
  afterEach(() => {
    fakeRekognition.detectFaces = ogDetectFaces;
  });
  for(let language of ['id','es','en']){
    it(`faceDetected moderation for language- ${language}, on multiple images` ,async()=>{

      let res=await initApp(0);//create a user
      const interestId = res.interests[0]._id;

      //post a question
      resQuestion =await postQuestion(0,{
        interestId: interestId,
        title: 'title1',
        text: 'text1',
        language,
      });


      images = JSON.stringify([
        { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
        { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
        { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
      ]),

      res = await request(app)
        .post('/v1/question/images')
        .set('authorization', 0)
        .query({ questionId: resQuestion._id })
        .attach('images', validImagePath)  // Attach the first image
        .attach('images', validImagePath)  // Attach the second image
        .attach('images', validImagePath) // Attach more images if needed
        .field('imagesArray', images)
      expect(res.status).to.equal(200);
      console.log('upload res :', res.body)
      await waitMs(10);

      let questionData = await Question.findOne({ _id: resQuestion._id }, { nonDecayedScore: 1, faceDetected: 1 });
      console.log('questionData: ', questionData)
      expect(questionData.nonDecayedScore).greaterThan(0);
      expect(questionData.faceDetected).eql(undefined);

      fakeRekognition.detectFaces = function (params) {
        const impl = function (resolve, reject) {
          console.log('Rekognition : detect-faces')
          resolve({ FaceDetails: [{ Confidence: 99.1 }] });
        };
        return {
          promise: () => new Promise(impl),
        };
      };

      res = await request(app)
        .post('/v1/question/images')
        .set('authorization', 0)
        .query({ questionId: resQuestion._id })
        .attach('images', validImagePath)  // Attach the first image
        .attach('images', validImagePath)  // Attach the second image
        .attach('images', validImagePath) // Attach more images if needed
        .field('imagesArray', images)
      expect(res.status).to.equal(200);
      console.log('upload res :', res.body)
      await waitMs(10);

      questionData = await Question.findOne({ _id: resQuestion._id }, { nonDecayedScore: 1, faceDetected: 1 });

      expect(questionData.nonDecayedScore).greaterThan(0);
      expect(questionData.faceDetected).to.eql(true);

      //delete images on question
      res = await request(app)
        .post('/v1/question/images')
        .set('authorization', 0)
        .query({ questionId: resQuestion._id })
        .field('imagesArray', JSON.stringify([]))
      expect(res.status).to.equal(200);
      console.log('upload res :', res.body)
      await waitMs(10);
      questionData = await Question.findOne({ _id: resQuestion._id }, { faceDetected: 1 });
      console.log('questionData after delete images: ', questionData)
      expect(questionData.faceDetected).to.eql(undefined);
    });
  }
});

it('shadow ban if 10 posts banned within 30 days', async () => {
  setMockPromptResponse(JSON.stringify({ ban: true, explanation: 'spam' }));

  clock = sinon.useFakeTimers();

  await initApp(0);
  await initApp(1);

  // 5 questions
  for (let i = 0; i < 5; i++) {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: i.toString(),
      });
    expect(res.status).to.equal(200);
    id = res.body._id;

    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: id, reason: ['Spam'], explanation: 'seems like spam' });
    expect(res.status).to.equal(200);

    post = await Question.findById(id);
    expect(post.banned).to.equal(true);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
  }

  // 35 days
  clock.tick(35 * 24 * 3600 * 1000);

  // 10 questions - user banned on the 10th question
  for (let i = 0; i < 10; i++) {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: i.toString(),
        text: i.toString(),
      });
    expect(res.status).to.equal(200);
    id = res.body._id;

    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: id, reason: ['Spam'], explanation: 'seems like spam' });
    expect(res.status).to.equal(200);

    post = await Question.findById(id);
    expect(post.banned).to.equal(true);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(i == 9);
    expect(user.bannedReason).to.equal(i == 9 ? '10 posts banned within 30 days' : undefined);
  }
});

it('ban if safety system error', async () => {
  // throw error
  fakeOpenaiClient.chat = {
    completions: {
      async create(params) {
        console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
        throw new Error('Your input image may contain content that is not allowed by our safety system');
      }
    }
  };

  setMockPromptResponse(JSON.stringify({ ban: true, explanation: 'spam' }));

  await initApp(0);
  await initApp(1);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'chess',
      title: '0',
    });
  expect(res.status).to.equal(200);
  id = res.body._id;

  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 1)
    .send({ questionId: id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  post = await Question.findById(id);
  expect(post.banned).to.equal(true);

  // admin checks reports
  user = await User.findOne({ _id: 0 });
  user.admin = true;
  user.adminPermissions = { all: true };
  await user.save();

  res = await request(app)
    .get('/v1/admin/user/postReports')
    .set('authorization', 0)
    .query({ user: '0' });
  expect(res.status).to.equal(200);
  expect(res.body.reports.length).to.equal(1);
  console.log(res.body.reports[0]);
  expect(res.body.reports[0].reportedQuestion._id).to.equal(id);
  expect(res.body.reports[0].reportedQuestion.title).to.equal('0');
  expect(res.body.reports[0].prompt).to.equal();
  expect(res.body.reports[0].openaiBan).to.equal(true);
});

it('dismiss if invalid image error', async () => {
  // throw error
  fakeOpenaiClient.chat = {
    completions: {
      async create(params) {
        console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
        throw new Error('Invalid Image');
      }
    }
  }

  await initApp(0);
  await initApp(1);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'chess',
      title: '0',
    });
  expect(res.status).to.equal(200);
  id = res.body._id;

  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 1)
    .send({ questionId: id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  post = await Question.findById(id);
  expect(post.banned).to.equal(false);

  setMockPromptResponse(JSON.stringify({ ban: true, explanation: 'spam' }));

  // second report should be processed since first was an error
  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 1)
    .send({ questionId: id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  post = await Question.findById(id);
  expect(post.banned).to.equal(true);
});

it('ignore subsequent reports after dismissing', async () => {
  setMockPromptResponse(JSON.stringify({ ban: false, explanation: 'not spam' }));

  await initApp(0);
  await initApp(1);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'chess',
      title: '0',
    });
  expect(res.status).to.equal(200);
  id = res.body._id;

  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 1)
    .send({ questionId: id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  post = await Question.findById(id);
  expect(post.banned).to.equal(false);

  // second report should be ignored
  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 1)
    .send({ questionId: id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  post = await Question.findById(id);
  expect(post.banned).to.equal(false);
});

it('limits', async () => {
  // limit 10 post reports per 24 hours
  // ignore if report success ratio over past 30 days is less than 10%

  setMockPromptResponse(JSON.stringify({ ban: false }));

  clock = sinon.useFakeTimers();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  // 10 reports from the same user
  for (let i = 1; i <= 10; i++) {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'chess',
        title: i.toString(),
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 0)
      .send({ questionId: q1Id, reason: ['Spam'], explanation: 'seems like spam' });
    expect(res.status).to.equal(200);

    reports = await PostReport.find();
    expect(reports.length).to.equal(i);
  }

  // next reports are ignored
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 2)
    .send({
      interestName: 'chess',
      title: '11',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 0)
    .send({ questionId: q1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  reports = await PostReport.find();
  expect(reports.length).to.equal(10);

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 2)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: '11',
    });
  expect(res.status).to.equal(200);
  c1Id = res.body._id;

  res = await request(app)
    .patch('/v1/comment/report')
    .set('authorization', 0)
    .send({ commentId: c1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  reports = await PostReport.find();
  expect(reports.length).to.equal(10);

  // a different user can still report
  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 1)
    .send({ questionId: q1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  reports = await PostReport.find().sort('-_id');
  expect(reports.length).to.equal(11);
  expect(reports[0].reportedBy).to.equal('1');

  res = await request(app)
    .patch('/v1/comment/report')
    .set('authorization', 1)
    .send({ commentId: c1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  reports = await PostReport.find().sort('-_id');
  expect(reports.length).to.equal(12);
  expect(reports[0].reportedBy).to.equal('1');

  // 24 hours
  clock.tick(1 * 24 * 3600 * 1000);

  // still not allowed because report success ratio is less than 10%
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 2)
    .send({
      interestName: 'chess',
      title: '12',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 2)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: '12',
    });
  expect(res.status).to.equal(200);
  c1Id = res.body._id;

  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 0)
    .send({ questionId: q1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  reports = await PostReport.find();
  expect(reports.length).to.equal(12);

  res = await request(app)
    .patch('/v1/comment/report')
    .set('authorization', 0)
    .send({ commentId: c1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  reports = await PostReport.find();
  expect(reports.length).to.equal(12);

  // 30 days
  clock.tick(30 * 24 * 3600 * 1000);

  // new reports are allowed
  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 0)
    .send({ questionId: q1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  reports = await PostReport.find().sort('-_id');
  expect(reports.length).to.equal(13);
  expect(reports[0].reportedBy).to.equal('0');

  res = await request(app)
    .patch('/v1/comment/report')
    .set('authorization', 0)
    .send({ commentId: c1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  reports = await PostReport.find().sort('-_id');
  expect(reports.length).to.equal(14);
  expect(reports[0].reportedBy).to.equal('0');

  clock.restore();
});

it('test question report with multiple images', async () => {
  await initApp(0);
  await initApp(1);

  let res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'chess',
      title: 'first post',
      mediaUploadPending: true,
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  let images = JSON.stringify([
    { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
    { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
    { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
  ]);

  res = await request(app)
    .post('/v1/question/images')
    .set('authorization', 0)
    .query({ questionId: q1Id })
    .attach('images', validImagePath)
    .attach('images', validImagePath)
    .attach('images', validImagePath)
    .field('imagesArray', images);
  expect(res.status).to.equal(200);
  images = res.body.images.map(img => IMAGE_DOMAIN + img.image);

  res = await request(app)
    .patch('/v1/question/report')
    .set('authorization', 1)
    .send({ questionId: q1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  let reports = await PostReport.find({});
  expect(reports.length).to.equal(1);
  expect(reports[0].reportedQuestion.toString()).to.equal(q1Id.toString());
  expect(reports[0].model).to.equal('gpt-4o-mini');
  const prompt = JSON.parse(reports[0].prompt);
  expect(prompt.imageUrls.length).to.equal(3);
  expect(prompt.imageUrls).to.eql(images);
});
