# Subcategory Pagination Implementation

## Overview

This implementation creates a new paginated collection system to improve performance for the `/web/cached/profiles/subcategory/sitemap` route by replacing expensive `skip` and `limit` operations with pre-computed paginated data.

## Problem

The original sitemap route was experiencing performance degradation due to frequent use of `skip` and `limit` operations on large collections, especially for higher page numbers.

## Solution

### 1. New Collection Schema (`models/subcategory-paginated-profiles.js`)

```javascript
{
  subcategoryId: Number,    // The subcategory ID
  pageNo: Number,          // Page number (1-based)
  profileIds: [Number],    // Array of profile IDs (max 100 per page)
  createdAt: Date,
  updatedAt: Date
}
```

**Indexes:**
- Compound unique index: `{ subcategoryId: 1, pageNo: 1 }`
- Cleanup index: `{ subcategoryId: 1 }`

### 2. Configuration Constants (`lib/constants.js`)

Added `getSitemapPaginationPageSize()` function:
- **Test environment**: 2 profiles per page
- **Production/Beta**: 100 profiles per page

### 3. Pagination Library (`lib/subcategory-pagination.js`)

**Key Functions:**
- `getAllSubcategoryIds()` - Get all subcategory IDs
- `getPaginatedProfileIds(subcategoryId, pageNo)` - Get profile IDs for a specific page
- `createPaginatedProfilesForSubcategory(subcategoryId)` - Create paginated data for a subcategory
- `processSubcategory(subcategoryId)` - Complete processing of a single subcategory
- `backfillSubcategoryPagination(options)` - Backfill all subcategories

**Profile Sorting:**
Profiles are sorted by `sort: 1, id: 1` as requested (ascending order).

### 4. Updated Subcategory Schema (`models/subcategory.js`)

Added `totalProfilePages` field to track the total number of pages for each subcategory.

### 5. Enhanced Sitemap Route (`routes/web.js`)

The route now:
1. **Checks for paginated data** when page size matches the configured pagination size
2. **Uses paginated collection** for optimal performance when available
3. **Falls back to original method** for backward compatibility when:
   - Page size doesn't match pagination size
   - `lastProfileId` parameter is provided
   - Paginated data is not available

### 6. Backfill Script (`scripts/backfill-subcategory-pagination.js`)

Command-line script to populate the paginated collection:

```bash
# Basic usage
node scripts/backfill-subcategory-pagination.js

# With options
node scripts/backfill-subcategory-pagination.js --batch-size 10 --no-log

# Dry run to see what would be processed
node scripts/backfill-subcategory-pagination.js --dry-run
```

**Features:**
- Batch processing to avoid overwhelming the database
- Progress logging
- Error handling and reporting
- Graceful shutdown handling

## Usage

### 1. Run the Backfill

```bash
node scripts/backfill-subcategory-pagination.js
```

This will:
- Process all subcategories in batches
- Create paginated profile data
- Update `totalProfilePages` in subcategory collection
- Clean up any excess pages

### 2. Route Behavior

The sitemap route automatically detects and uses the paginated collection when:
- Page size equals the configured pagination size (100 for prod/beta, 2 for test)
- No `lastProfileId` parameter is provided
- Paginated data exists for the subcategory

Otherwise, it falls back to the original skip/limit method for backward compatibility.

## Performance Benefits

1. **Eliminates expensive skip operations** for higher page numbers
2. **Pre-computed pagination** reduces database query complexity
3. **Indexed lookups** on `{ subcategoryId: 1, pageNo: 1 }`
4. **Maintains profile order** through pre-sorted arrays

## Testing

Comprehensive test suite in `test/subcategory-pagination.js` covers:
- Library function testing
- Backfill functionality
- Route integration testing
- Edge cases and error handling
- Backward compatibility

## Maintenance

### Adding New Profiles

When new profiles are added to subcategories, run the backfill script to update the paginated data:

```bash
node scripts/backfill-subcategory-pagination.js
```

### Monitoring

The backfill script provides detailed logging and error reporting to monitor the health of the pagination system.

## Backward Compatibility

The implementation maintains full backward compatibility:
- Original route behavior is preserved when paginated data is not available
- All existing query parameters continue to work
- No breaking changes to the API response format

## Files Modified/Created

### New Files:
- `models/subcategory-paginated-profiles.js` - New collection schema
- `lib/subcategory-pagination.js` - Pagination library
- `test/subcategory-pagination.js` - Test suite
- `scripts/backfill-subcategory-pagination.js` - Backfill script
- `docs/subcategory-pagination-implementation.md` - This documentation

### Modified Files:
- `lib/constants.js` - Added pagination page size configuration
- `models/subcategory.js` - Added `totalProfilePages` field
- `routes/web.js` - Enhanced sitemap route with pagination support
- `test/web.js` - Updated existing tests for compatibility
