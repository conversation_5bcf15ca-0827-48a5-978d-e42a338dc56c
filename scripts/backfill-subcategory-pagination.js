#!/usr/bin/env node

/**
 * Script to backfill subcategory pagination data
 * 
 * Usage:
 *   node scripts/backfill-subcategory-pagination.js
 *   
 * Options:
 *   --batch-size <number>  Number of subcategories to process in parallel (default: 5)
 *   --no-log             Disable progress logging
 *   --dry-run            Show what would be processed without making changes
 */

const { backfillSubcategoryPagination } = require('../lib/subcategory-pagination');
const { getSitemapPaginationPageSize } = require('../lib/constants');

async function main() {
  const args = process.argv.slice(2);
  
  // Parse command line arguments
  let batchSize = 5;
  let logProgress = true;
  let dryRun = false;
  
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--batch-size':
        batchSize = parseInt(args[i + 1]);
        i++; // Skip next argument
        break;
      case '--no-log':
        logProgress = false;
        break;
      case '--dry-run':
        dryRun = true;
        break;
      case '--help':
      case '-h':
        console.log(`
Usage: node scripts/backfill-subcategory-pagination.js [options]

Options:
  --batch-size <number>  Number of subcategories to process in parallel (default: 5)
  --no-log             Disable progress logging
  --dry-run            Show what would be processed without making changes
  --help, -h           Show this help message
        `);
        process.exit(0);
    }
  }
  
  if (logProgress) {
    console.log('='.repeat(60));
    console.log('Subcategory Pagination Backfill Script');
    console.log('='.repeat(60));
    console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`Page size: ${getSitemapPaginationPageSize()}`);
    console.log(`Batch size: ${batchSize}`);
    console.log(`Dry run: ${dryRun ? 'Yes' : 'No'}`);
    console.log('='.repeat(60));
  }
  
  if (dryRun) {
    const { getAllSubcategoryIds } = require('../lib/subcategory-pagination');
    const subcategoryIds = await getAllSubcategoryIds();
    console.log(`Would process ${subcategoryIds.length} subcategories:`);
    console.log(subcategoryIds.slice(0, 10).join(', ') + (subcategoryIds.length > 10 ? '...' : ''));
    console.log('\nRun without --dry-run to execute the backfill.');
    return;
  }
  
  try {
    const startTime = Date.now();
    
    const result = await backfillSubcategoryPagination({
      batchSize,
      logProgress
    });
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    if (logProgress) {
      console.log('='.repeat(60));
      console.log('BACKFILL COMPLETED SUCCESSFULLY');
      console.log('='.repeat(60));
      console.log(`Duration: ${duration} seconds`);
      console.log(`Total subcategories: ${result.totalSubcategories}`);
      console.log(`Successfully processed: ${result.processedSubcategories}`);
      console.log(`Total pages created: ${result.totalPages}`);
      console.log(`Total profiles processed: ${result.totalProfiles}`);
      console.log(`Errors: ${result.errors.length}`);
      
      if (result.errors.length > 0) {
        console.log('\nErrors encountered:');
        result.errors.forEach(error => {
          console.log(`  Subcategory ${error.subcategoryId}: ${error.error}`);
        });
      }
      
      console.log('='.repeat(60));
    }
    
    // Exit with error code if there were failures
    if (result.errors.length > 0) {
      process.exit(1);
    }
    
  } catch (error) {
    console.error('Fatal error during backfill:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\nReceived SIGINT. Gracefully shutting down...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\nReceived SIGTERM. Gracefully shutting down...');
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = { main };
