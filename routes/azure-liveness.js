const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
// const sharp = require('sharp');
const httpErrors = require('../lib/http-errors');
const s3 = require('../lib/s3');
const AzureLivenessChallenge = require('../models/azure-liveness-challenge');
const { createLivenessSession, getLivenessResult, getSessionImage } = require('../lib/azure-face');
const { setFaceComparisonReferenceImage, verifyProfilePicture } = require('../lib/verification');
const userLib = require('../lib/user');
const socketLib = require('../lib/socket');
const { findBannedFace } = require('../lib/rekognition');
const reportLib = require('../lib/report');

async function checkLivenessUploadAllowed(req, res, next) {
  if (!req.query.challengeId) {
    return next(httpErrors.forbiddenError());
  }
  const challenge = await AzureLivenessChallenge.findOne({ id: req.query.challengeId } );
  if (!challenge) {
    return next(httpErrors.forbiddenError());
  }
  if (challenge.frames.length > 20) {
    return next(httpErrors.forbiddenError());
  }
  req.challenge = challenge;
  return next();
}
module.exports = function () {
  router.post('/session', asyncHandler(async(req, res, next) => {
    const userId = req.user._id;
    const deviceCorrelationId = req.user.deviceId;
    const result = await createLivenessSession(deviceCorrelationId);
    if (result.error) {
      switch (result.error.code) {
        case 'Unspecified':
          // Access denied due to invalid subscription key.
          // Make sure you are subscribed to an API you are trying to call and provide the right key.
          return next(httpErrors.forbiddenError());
        case 'Forbidden':
          // Out of call volume quota. Quota will be replenished in 2 days.
          return next(httpErrors.forbiddenError());
        case 'BadArgument':
          return next(httpErrors.badRequestError());
        case 'TooManyRequest':
          return next(httpErrors.unauthenticatedError());
        default:
          throw httpErrors.unauthenticatedError();
      }
    }
    const challenge = new AzureLivenessChallenge({
      id: result.data.sessionId,
      user: userId,
      deviceCorrelationId: result.data.deviceCorrelationId
    });
    await challenge.save();

    res.json({
      challengeId: challenge.id,
      authToken: result.data.authToken
    })
  }));

  router.post('/result', asyncHandler(checkLivenessUploadAllowed), asyncHandler(async (req, res, next) => {
    const user = req.user;
    const challenge = req.challenge;
    if (!challenge || challenge.user !== user._id) {
      return next(httpErrors.forbiddenError());
    }

    user.verification.method = 'face';
    if (!user.events.finished_signup) {
      user.verification.attemptedVerificationDuringSignup = true;
    }
    await user.save();

    // check if user is shadow banned, we need to immediately
    if (user.shadowBanned) {
      await user.setVerificationStatus('rejected');
      user.verification.verifiedBy = null;
      user.verification.verifiedDate = Date.now();
      user.verification.rejectionReason = 'Face challenge failed.';
      challenge.rejectionReason = 'User is banned.';
      challenge.livenessSuccess = null;
      challenge.livenessFailureReason = null;
      await challenge.save();
      user.livenessVerification = challenge;
      await user.save();

      return res.json({
        verificationStatus: user.verification.status,
        rejectionReason: user.verification.rejectionReason,
      });
    }

    if (user.isVerified()) {
      // ignore if user is already verified
      return res.json({
        verificationStatus: 'verified',
      });
    }

    // try up to 3 times to get the result, break if error or result available
    let livenessResult;
    for (let i = 0; i < 3; i++) {
      livenessResult = await getLivenessResult(challenge.id);
      if (livenessResult.error) {
        break;
      }
      if (livenessResult.data?.status == 'ResultAvailable') {
        break;
      }
      // sleep for 5 seconds before retrying
      await new Promise((r) => setTimeout(r, 5000));
    }

    let livenessDecision;
    challenge.fullResult = livenessResult;
    if (livenessResult.error) {
      challenge.livenessSuccess = false;
      challenge.livenessFailureReason = livenessResult.error.message;
      await challenge.save();

      if (livenessResult.error) {
        switch (livenessResult.error.code) {
          case 'Unspecified':
            // Access denied due to invalid subscription key.
            // Make sure you are subscribed to an API you are trying to call and provide the right key.
            return next(httpErrors.forbiddenError());
          case 'Forbidden':
            // Out of call volume quota. Quota will be replenished in 2 days.
            return next(httpErrors.forbiddenError());
          case 'BadArgument':
            return next(httpErrors.badRequestError());
          case 'TooManyRequest':
            return next(httpErrors.unauthenticatedError());
          default:
            return next(httpErrors.forbiddenError());
        }
      }

      // liveness detection error, we need a manual verification
      await user.setVerificationStatus('pending')
      challenge.rejectionReason = 'Automated face comparison could not be performed.';
    } else {

      // store liveness image
      // sharp installation might be causing issues, so disabling this section since
      // azure liveness is not live yet
      /*
      if (livenessResult?.data?.result?.sessionImageId) {
        const getSessionImageResult = await getSessionImage(livenessResult.data.result.sessionImageId);
        // convert to png because aws rekognition cannot handle webp
        const png_data = await sharp(getSessionImageResult.data).toFormat('png').toBuffer();
        const path = await s3.uploadUserImage(user._id, png_data, 'png');
        user.verification.pictures.push(path);
        user.verification.pictureUploadedAt = Date.now();
        setFaceComparisonReferenceImage(user, path);

        challenge.frames.push({
          timestamp: Date.now(),
          key: path,
        });
      }
      */

      // liveness process successfully completed
      if (livenessResult.data && livenessResult.data.result && livenessResult.data.result.response) {
        const resultResponse = livenessResult.data.result.response;
        challenge.latencyMillis = resultResponse.latencyInMilliseconds; // not sure we need this
        if (resultResponse.body) {
          const resultBody = resultResponse.body;
          challenge.livenessDecision = resultBody.livenessDecision;
          challenge.livenessModelVersion = resultBody.modelVersionUsed;
          livenessDecision = resultBody.livenessDecision;
          if (resultBody.target) {
            const resultTarget = resultBody.target;
            challenge.livenessImageType = resultTarget.imageType;
            if (resultTarget.faceRectangle) {
              const resultFaceRectangle = resultTarget.faceRectangle;
              challenge.faceTop = resultFaceRectangle.top;
              challenge.faceLeft = resultFaceRectangle.left;
              challenge.faceWidth = resultFaceRectangle.width;
              challenge.faceHeight = resultFaceRectangle.height;
            }
          }
          if (resultBody.error) {
            challenge.errorCode = resultBody.error.code;
            challenge.errorMessage = resultBody.error.message;
          }
        }
      }
      challenge.livenessSuccess = true;
      if (!livenessDecision) {
        // no liveness decision, liveness process might return error
        await user.setVerificationStatus('pending');
        challenge.rejectionReason = 'Automated face comparison could not be performed.';
      } else {
        switch (livenessDecision) {
          case 'spoofface':
            await user.setVerificationStatus('rejected');
            user.verification.verifiedBy = null;
            user.verification.verifiedDate = Date.now();
            user.verification.rejectionReason = 'Face challenge failed.';
            challenge.rejectionReason = 'Face challenge failed.';
            break;
          case 'realface':
            // do face comparison process here
            const result = await verifyProfilePicture(user);
            if (result === 'verify') {
              await user.setVerificationStatus('verified');
              user.verification.verifiedBy = null;
              user.verification.verifiedDate = Date.now();
              user.verification.newReverificationSystem = userLib.useNewReverification(user);
              challenge.compareFacesSuccess = true;
            } else if (result === 'manual') {
              await user.setVerificationStatus('pending');
              challenge.rejectionReason = 'Automated face comparison could not be performed.';
            } else {
              await user.setVerificationStatus('rejected');
              user.verification.verifiedBy = null;
              user.verification.verifiedDate = Date.now();
              user.verification.rejectionReason = 'Make sure your first profile picture is a picture of you, and only you.';
              challenge.rejectionReason = 'Verification does not match profile pictures.';
              challenge.compareFacesSuccess = false;
            }
            break;
          default: // uncertain
            await user.setVerificationStatus('pending');
            challenge.rejectionReason = 'Automated face comparison could not be performed.';
            break;
        }
      }
    }
    await challenge.save();
    user.livenessVerification = challenge;
    await user.save();

    if (user.verification.status == 'verified') {
      await socketLib.grantVerifyProfileAward(user);
    }

    if (user.livenessVerification.frames.length > 0) {
      const bannedFace = await findBannedFace(user.livenessVerification.frames[0].key);
      if (bannedFace) {
        await reportLib.banDueToBannedFaceFound(req.user, 'liveness verification picture', user.livenessVerification.frames[0].key, bannedFace.Face.ExternalImageId);
      }
    }

    res.json({
      verificationStatus: user.verification.status,
      rejectionReason: user.verification.rejectionReason,
    });
  }));

  return router;
};
